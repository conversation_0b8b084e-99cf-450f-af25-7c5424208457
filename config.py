"""
Configuration constants for the DaBot Agent.

This module contains all configuration constants and settings for the application.
"""

from typing import Dict, Any

APP_VERSION = "0.1"
APP_NAME = "DaBot Agent"
LLAMACPP_API_URL = "http://172.16.0.111:11111/completion"
LLAMACPP_MAX_TOKENS = 250  # n_predict
MODEL_PROVIDER = "llamacpp"
MAX_ITERATIONS = "1"
VERBOSE = "true"
LOG_LEVEL = "DEBUG"
LOG_FILE = "dabot-agent.log"
RESERVED_TOKENS = 50

# Request behavior flags
LLAMACPP_STREAM = False  # Exposed via config for experimentation


# Helper Functions
def get_model_config() -> Dict[str, Any]:
    """Get model configuration dictionary."""
    from utils.model_info import get_model_name_from_server

    dynamic_model_name = get_model_name_from_server()

    return {
            "provider"  : MODEL_PROVIDER,
            "name": dynamic_model_name,
            "max_tokens": <PERSON>AM<PERSON><PERSON>_MAX_TOKENS,
            "api_url"   : LLAMACPP_API_URL
    }


def get_agent_config() -> Dict[str, Any]:
    """Get agent configuration dictionary."""
    return {
            "max_iterations": MAX_ITERATIONS,
            "verbose"       : VERBOSE
    }


def get_logging_config() -> Dict[str, Any]:
    """Get logging configuration dictionary."""
    return {
            "level": LOG_LEVEL,
            "file" : LOG_FILE
    }
