{
  "timestamp": "czw, 7 sie 2025, 17:57:54 CEST",
  "test_description": "Dynamic context verification with improved system",
  "setup_info": {
    "server_info": {
      "model_name": "Finance-Llama-8B-GGUF-q4_K_M.gguf",
      "context_length": 2000,
      "server_available": true,
      "props": {
        "default_generation_settings": {
          "id": 0,
          "id_task": -1,
          "n_ctx": 2000,
          "speculative": false,
          "is_processing": false,
          "params": {
            "n_predict": -1,
            "seed": 4294967295,
            "temperature": 0.30000001192092896,
            "dynatemp_range": 0.0,
            "dynatemp_exponent": 1.0,
            "top_k": 20,
            "top_p": 0.949999988079071,
            "min_p": 0.05000000074505806,
            "top_n_sigma": -1.0,
            "xtc_probability": 0.0,
            "xtc_threshold": 0.10000000149011612,
            "typical_p": 1.0,
            "repeat_last_n": 64,
            "repeat_penalty": 1.2999999523162842,
            "presence_penalty": 0.0,
            "frequency_penalty": 0.0,
            "dry_multiplier": 0.0,
            "dry_base": 1.75,
            "dry_allowed_length": 2,
            "dry_penalty_last_n": 4000,
            "dry_sequence_breakers": [
              "\n",
              ":",
              "\"",
              "*"
            ],
            "mirostat": 2,
            "mirostat_tau": 5.0,
            "mirostat_eta": 0.10000000149011612,
            "stop": [],
            "max_tokens": -1,
            "n_keep": 0,
            "n_discard": 0,
            "ignore_eos": false,
            "stream": true,
            "logit_bias": [],
            "n_probs": 0,
            "min_keep": 0,
            "grammar": "",
            "grammar_lazy": false,
            "grammar_triggers": [],
            "preserved_tokens": [],
            "chat_format": "Content-only",
            "reasoning_format": "none",
            "reasoning_in_content": false,
            "thinking_forced_open": false,
            "samplers": [
              "penalties",
              "dry",
              "top_n_sigma",
              "top_k",
              "typ_p",
              "top_p",
              "min_p",
              "xtc",
              "temperature"
            ],
            "speculative.n_max": 16,
            "speculative.n_min": 0,
            "speculative.p_min": 0.75,
            "timings_per_token": false,
            "post_sampling_probs": false,
            "lora": []
          },
          "prompt": "",
          "next_token": {
            "has_next_token": true,
            "has_new_line": false,
            "n_remain": -1,
            "n_decoded": 0,
            "stopping_word": ""
          }
        },
        "total_slots": 2,
        "model_path": "/models/Finance-Llama-8B-GGUF-q4_K_M.gguf",
        "modalities": {
          "vision": false,
          "audio": false
        },
        "chat_template": "{%- for message in messages -%}\n  {{- '<|im_start|>' + message.role + '\n' + message.content + '<|im_end|>\n' -}}\n{%- endfor -%}\n{%- if add_generation_prompt -%}\n  {{- '<|im_start|>assistant\n' -}}\n{%- endif -%}",
        "bos_token": "<|begin_of_text|>",
        "eos_token": "<|end_of_text|>",
        "build_info": "b5783-eb3fa291"
      }
    },
    "dynamic_context": 2000,
    "llm_context": 2000,
    "llm": 