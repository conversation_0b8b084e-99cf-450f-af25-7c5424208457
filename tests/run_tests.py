#!/usr/bin/env python3
"""
Simplified LLM Testing Framework Runner

This script provides a unified interface for testing LLMs up to 30B q4-q6 models:
- Simple model testing with essential capabilities
- Progressive complexity testing for token limit discovery (max 500 tokens)
- Model-specific logging with prompts and responses

Usage:
    python tests/run_tests.py

Features:
- Interactive model selection
- Essential test suite for small to medium LLMs
- Financial question testing
- Automated logging and reporting
"""

import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.unified_model_test import run_unified_model_test
from tests.result_processor import process_test_results


def display_banner():
    """Display the testing framework banner."""
    print("🔬" + "=" * 60 + "🔬")
    print("   UNIFIED LLM TESTING FRAMEWORK")
    print("   Comprehensive Testing for Small to Medium LLMs (up to 30B)")
    print("🔬" + "=" * 60 + "🔬")
    print()


def main():
    """Main test runner interface - runs unified test automatically."""
    display_banner()

    try:
        # Run unified test (combines simple + complexity tests)
        results = run_unified_model_test()
        print("\n👋 Unified test completed. Thank you for using the LLM Testing Framework!")

        # Process test results for averaging and comparison
        print("\n🔄 Processing test results...")
        process_test_results()

    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
