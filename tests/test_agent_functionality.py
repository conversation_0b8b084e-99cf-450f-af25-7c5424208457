#!/usr/bin/env python3
"""
Core functionality test for DaBot Agent LangGraph Edition.

This test verifies that the core agent functionality works correctly,
including state management, node execution, and conversation flow.
"""

import os
import sys
import unittest
from unittest.mock import patch, Mock

from langchain.schema.messages import HumanMessage, AIMessage

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class TestAgentFunctionality(unittest.TestCase):
    """Test core agent functionality."""

    def setUp(self):
        """Set up test fixtures."""
        from core.state import AgentState
        self.initial_state = AgentState(
                user_input = "What is 2 + 2?",
                messages = [],
                final_answer = "",
                error = None
        )

    def test_state_management(self):
        """Test agent state creation and manipulation."""
        from core.state import AgentState

        # Test empty state
        empty_state = AgentState()
        self.assertIsInstance(empty_state, dict)

        # Test state with data
        state = AgentState(
                user_input = "test",
                messages = [HumanMessage(content = "Hello")],
                final_answer = "Hi there!",
                error = None
        )

        self.assertEqual(state["user_input"], "test")
        self.assertEqual(len(state["messages"]), 1)
        self.assertEqual(state["final_answer"], "Hi there!")
        self.assertIsNone(state["error"])
        print("✅ State management test passed")

    def test_summarization_node_no_trigger(self):
        """Test summarization node when summarization is not triggered."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        # Create state with few messages (below threshold)
        state = AgentState(
                messages = [
                        HumanMessage(content = "Hello"),
                        AIMessage(content = "Hi there!")
                ],
                window_size = 5
        )

        result = summarization_node(state)

        # Should not trigger summarization
        self.assertEqual(len(result["messages"]), 2)
        self.assertNotIn("summary", result)
        print("✅ Summarization node (no trigger) test passed")

    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_node_with_trigger(self, mock_get_llm):
        """Test summarization node when summarization is triggered."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        # Mock LLM response
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.content = "Summary: User asked about math, assistant helped with calculations."
        mock_llm.invoke.return_value = mock_response
        mock_llm.prompt_manager.count_tokens.return_value = 10
        mock_get_llm.return_value = mock_llm

        # Create state with many messages (above threshold)
        messages = []
        for i in range(7):  # More than window_size of 3
            messages.append(HumanMessage(content = f"Message {i}"))
            messages.append(AIMessage(content = f"Response {i}"))

        state = AgentState(
                messages = messages,
                window_size = 3
        )

        result = summarization_node(state)

        # Should trigger summarization
        self.assertIn("summary", result)
        self.assertEqual(len(result["messages"]), 3)  # Pruned to window_size
        self.assertIn("Summary:", result["summary"])
        print("✅ Summarization node (with trigger) test passed")

    @patch('core.nodes.llm_node.get_cached_llm')
    def test_llm_node_basic(self, mock_get_llm):
        """Test basic LLM node functionality."""
        from core.nodes.llm_node import llm_node
        from core.state import AgentState

        # Mock LLM response
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.content = "2 + 2 equals 4."
        mock_llm.invoke.return_value = mock_response
        mock_llm.prompt_manager.count_tokens.return_value = 5
        mock_llm.prompt_manager.max_context_tokens = 2048
        mock_llm.prompt_manager.reserved_tokens = 50
        mock_get_llm.return_value = mock_llm

        state = AgentState(
                user_input = "What is 2 + 2?",
                messages = [HumanMessage(content = "What is 2 + 2?")]
        )

        result = llm_node(state)

        self.assertEqual(result["final_answer"], "2 + 2 equals 4.")
        self.assertIn("messages", result)
        # Should have added AI message
        self.assertTrue(any(isinstance(msg, AIMessage) for msg in result["messages"]))
        print("✅ LLM node basic test passed")

    @patch('core.nodes.llm_node.get_cached_llm')
    def test_llm_node_with_history(self, mock_get_llm):
        """Test LLM node with conversation history."""
        from core.nodes.llm_node import llm_node
        from core.state import AgentState

        # Mock LLM response
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.content = "The result is 6."
        mock_llm.invoke.return_value = mock_response
        mock_llm.prompt_manager.count_tokens.return_value = 5
        mock_llm.prompt_manager.max_context_tokens = 2048
        mock_llm.prompt_manager.reserved_tokens = 50
        mock_get_llm.return_value = mock_llm

        state = AgentState(
                user_input = "What about 3 + 3?",
                messages = [
                        HumanMessage(content = "What is 2 + 2?"),
                        AIMessage(content = "2 + 2 equals 4."),
                        HumanMessage(content = "What about 3 + 3?")
                ]
        )

        result = llm_node(state)

        self.assertEqual(result["final_answer"], "The result is 6.")
        # Should preserve conversation history
        self.assertGreaterEqual(len(result["messages"]), 3)
        print("✅ LLM node with history test passed")

    @patch('core.nodes.llm_node.get_cached_llm')
    def test_llm_node_empty_response(self, mock_get_llm):
        """Test LLM node handling of empty response."""
        from core.nodes.llm_node import llm_node
        from core.state import AgentState

        # Mock empty LLM response
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.content = ""  # Empty response
        mock_llm.invoke.return_value = mock_response
        mock_llm.prompt_manager.count_tokens.return_value = 0
        mock_llm.prompt_manager.max_context_tokens = 2048
        mock_llm.prompt_manager.reserved_tokens = 50
        mock_get_llm.return_value = mock_llm

        state = AgentState(
                user_input = "Test question",
                messages = [HumanMessage(content = "Test question")]
        )

        result = llm_node(state)

        # Should provide fallback response
        self.assertIn("apologize", result["final_answer"].lower())
        self.assertIn("rephrase", result["final_answer"].lower())
        print("✅ LLM node empty response test passed")

    def test_tool_registry_basic(self):
        """Test basic tool registry functionality."""
        from core.tools.base import ToolRegistry, LangGraphTool

        registry = ToolRegistry()

        # Test adding a simple tool
        def test_tool(input_text: str) -> str:
            return f"Processed: {input_text}"

        tool = LangGraphTool(
                name = "test_tool",
                description = "A test tool",
                func = test_tool,
                is_async = False
        )

        registry.register_tool(tool)

        # Test tool retrieval
        retrieved_tool = registry.get_tool("test_tool")
        self.assertIsNotNone(retrieved_tool)
        self.assertEqual(retrieved_tool.name, "test_tool")

        # Test tool execution
        result = retrieved_tool.execute("hello")
        self.assertEqual(result, "Processed: hello")
        print("✅ Tool registry basic test passed")

    def test_datetime_tools(self):
        """Test datetime tools functionality."""
        from core.tools.local.datetime_tools import (
            get_current_date_tool,
            get_current_time_tool,
            get_current_datetime_tool
        )

        # Test date tool
        date_result = get_current_date_tool.execute("")
        self.assertIsInstance(date_result, str)
        self.assertRegex(date_result, r'\d{4}-\d{2}-\d{2}')

        # Test time tool
        time_result = get_current_time_tool.execute("")
        self.assertIsInstance(time_result, str)
        self.assertRegex(time_result, r'\d{2}:\d{2}:\d{2}')

        # Test datetime tool
        datetime_result = get_current_datetime_tool.execute("")
        self.assertIsInstance(datetime_result, str)
        self.assertIn(date_result.split()[0], datetime_result)
        print("✅ DateTime tools test passed")

    def test_math_calculator_tool(self):
        """Test math calculator tool functionality."""
        from core.tools.local.math_tools import math_calculator_tool

        # Test basic arithmetic
        result = math_calculator_tool.execute("2 + 2")
        self.assertIn("4", str(result))

        # Test more complex expression
        result = math_calculator_tool.execute("sqrt(16)")
        self.assertIn("4", str(result))

        # Test invalid expression
        result = math_calculator_tool.execute("invalid expression")
        self.assertIn("Error", str(result))
        print("✅ Math calculator tool test passed")


def main():
    """Run the core functionality tests."""
    print("🧪 Running DaBot Agent Core Functionality Tests")
    print("=" * 50)

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAgentFunctionality)

    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity = 2)
    result = runner.run(suite)

    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All core functionality tests passed!")
        print("✅ Your DaBot Agent core functionality is working correctly.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print(f"Failed: {len(result.failures)}, Errors: {len(result.errors)}")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
