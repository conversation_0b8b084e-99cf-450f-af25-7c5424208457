#!/usr/bin/env python3
"""
Fresh Context Test - Ensure no caching between LLM calls
"""

import json
import os
import sys
import time
import uuid

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llamacpp_provider import LlamaCppProvider
from models.prompt_manager import PromptManager
from logger.get_logger import get_logger
import config


def test_fresh_llm_call(target_tokens: int, test_id: str):
    """Test with completely fresh LLM instance and unique prompt."""
    logger = get_logger()

    # Create FRESH provider and LLM for each test
    provider = LlamaCppProvider(config = config.get_model_config())
    llm = provider.get_llm()

    # Initialize fresh prompt manager
    prompt_manager = PromptManager(max_context_tokens = config.MAX_CONTEXT_TOKENS,
                                   reserved_tokens = config.RESERVED_TOKENS)

    # Generate unique prompt to avoid server-side caching
    unique_id = str(uuid.uuid4())[:8]
    base_prompt = f"Test {test_id} (ID: {unique_id}): What is the highest number you can see in this list?\n\nNumbers: "

    # Build the number list
    numbers = []
    number = 1
    while True:
        test_numbers = numbers + [str(number)]
        test_prompt = base_prompt + " ".join(test_numbers) + f"\n\nAnswer (Test {test_id}):"
        token_count = prompt_manager.count_tokens(test_prompt)

        if token_count >= target_tokens:
            break

        numbers.append(str(number))
        number += 1

    final_prompt = base_prompt + " ".join(numbers) + f"\n\nAnswer (Test {test_id}):"
    final_token_count = prompt_manager.count_tokens(final_prompt)
    highest_number_in_sequence = number - 1

    print(f"🧪 Test {test_id}: {target_tokens} tokens")
    print(f"   Unique ID: {unique_id}")
    print(f"   Actual tokens: {final_token_count}")
    print(f"   Sequence: 1 to {highest_number_in_sequence}")
    print(f"   Prompt preview: {final_prompt[:100]}...")

    # Record start time
    start_time = time.time()

    try:
        from langchain.schema.messages import HumanMessage
        messages = [HumanMessage(content = final_prompt)]

        print(f"   🚀 Sending request at {time.strftime('%H:%M:%S')}...")
        result = llm._generate(messages)

        # Record end time
        end_time = time.time()
        response_time = end_time - start_time

        response = result.generations[0].message.content.strip()

        print(f"   ⏱️  Response time: {response_time:.2f} seconds")
        print(f"   📤 LLM Response: '{response}'")

        # Extract numbers from response
        import re
        numbers_in_response = re.findall(r'\b\d+\b', response)

        if numbers_in_response:
            highest_response_number = max(int(n) for n in numbers_in_response)
            visibility_percentage = (highest_response_number / highest_number_in_sequence) * 100
        else:
            highest_response_number = None
            visibility_percentage = 0

        print(f"   📊 Highest number seen: {highest_response_number}")
        print(f"   📈 Visibility: {visibility_percentage:.1f}%")

        # Check if response time is suspiciously fast
        if response_time < 1.0:
            print(f"   ⚠️  WARNING: Response time {response_time:.2f}s is suspiciously fast!")
            print(f"       This suggests caching or connection reuse.")

        return {
                "test_id"                   : test_id,
                "unique_id"                 : unique_id,
                "target_tokens"             : target_tokens,
                "actual_tokens"             : final_token_count,
                "highest_number_in_sequence": highest_number_in_sequence,
                "model_response"            : response,
                "highest_response_number"   : highest_response_number,
                "visibility_percentage"     : round(visibility_percentage, 1),
                "response_time_seconds"     : round(response_time, 2),
                "success"                   : True,
                "potentially_cached"        : response_time < 1.0
        }

    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time

        print(f"   ❌ Error after {response_time:.2f}s: {str(e)}")
        return {
                "test_id"                   : test_id,
                "unique_id"                 : unique_id,
                "target_tokens"             : target_tokens,
                "actual_tokens"             : final_token_count,
                "highest_number_in_sequence": highest_number_in_sequence,
                "model_response"            : None,
                "highest_response_number"   : None,
                "visibility_percentage"     : 0,
                "response_time_seconds"     : round(response_time, 2),
                "success"                   : False,
                "error"                     : str(e),
                "potentially_cached"        : False
        }


def run_fresh_context_tests():
    """Run fresh context tests with no caching."""

    print("🔬 FRESH CONTEXT TEST (No Caching)")
    print("=" * 80)
    print("Each test uses a fresh LLM instance and unique prompts to avoid caching...")
    print()

    # Test specific token counts with delays between tests
    test_cases = [500, 600, 700, 800, 900, 1000]

    results = []

    for i, tokens in enumerate(test_cases):
        test_id = f"T{i + 1:02d}"

        # Add delay between tests to ensure fresh connections
        if i > 0:
            print(f"   ⏳ Waiting 2 seconds before next test...")
            time.sleep(2)

        result = test_fresh_llm_call(tokens, test_id)
        results.append(result)

        if not result["success"]:
            print(f"   ❌ Test {test_id} failed, stopping tests.")
            break

        print()  # Empty line between tests

    # Analysis
    print(f"📊 FRESH CONTEXT ANALYSIS:")
    print("=" * 60)

    successful_results = [r for r in results if r["success"]]

    if successful_results:
        # Check for suspiciously fast responses
        fast_responses = [r for r in successful_results if r["potentially_cached"]]
        if fast_responses:
            print(f"⚠️  SUSPICIOUS: {len(fast_responses)} tests had suspiciously fast responses (<1s):")
            for r in fast_responses:
                print(f"   - Test {r['test_id']}: {r['response_time_seconds']}s")
        else:
            print(f"✅ All {len(successful_results)} tests had realistic response times (>1s)")

        # Response time analysis
        response_times = [r["response_time_seconds"] for r in successful_results]
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)

        print(f"\n⏱️  Response Time Analysis:")
        print(f"   Average: {avg_time:.2f}s")
        print(f"   Range: {min_time:.2f}s - {max_time:.2f}s")

        # Visibility analysis
        print(f"\n📈 Visibility Analysis:")
        for result in successful_results:
            status_icon = "✅" if result["visibility_percentage"] >= 95 else "🔴"
            print(f"   {status_icon} Test {result['test_id']} ({result['target_tokens']} tokens): "
                  f"{result['visibility_percentage']}% visibility, "
                  f"{result['response_time_seconds']}s")

        # Find practical limit
        good_results = [r for r in successful_results if r["visibility_percentage"] >= 95]
        if good_results:
            max_good_tokens = max(r["target_tokens"] for r in good_results)
            print(f"\n🎯 PRACTICAL LIMIT: {max_good_tokens} tokens (with >95% visibility)")

    # Save results
    results_file = "fresh_context_results.json"
    with open(results_file, 'w') as f:
        json.dump({
                "timestamp"          : str(os.popen('date').read().strip()),
                "test_description"   : "Fresh context test with no caching between calls",
                "server_reported_ctx": 1024,
                "config_max_context" : config.MAX_CONTEXT_TOKENS,
                "test_results"       : results
        }, f, indent = 2)

    print(f"\n📄 Detailed results saved to: {results_file}")

    return results


if __name__ == "__main__":
    import logging

    logging.basicConfig(level = logging.INFO, format = '%(asctime)s - %(levelname)s - %(message)s')

    try:
        results = run_fresh_context_tests()

        # Quick summary
        successful = [r for r in results if r["success"]]
        if successful:
            print(f"\n🎯 QUICK SUMMARY:")
            print(f"   Tests completed: {len(successful)}")

            # Check for caching issues
            potentially_cached = [r for r in successful if r["potentially_cached"]]
            if potentially_cached:
                print(f"   ⚠️  Potentially cached responses: {len(potentially_cached)}")
                print(f"   This suggests server-side caching or connection reuse issues.")
            else:
                print(f"   ✅ No caching detected - all responses took realistic time")

            # Show practical limit
            good_visibility = [r for r in successful if r["visibility_percentage"] >= 95]
            if good_visibility:
                max_tokens = max(r["target_tokens"] for r in good_visibility)
                print(f"   Safe token limit: {max_tokens} tokens")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
