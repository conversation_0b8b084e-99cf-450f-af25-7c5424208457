#!/usr/bin/env python3
"""
Test to verify that max_tokens is always calculated by prompt_manager.py

This test creates a real LlamaCppChatModel instance and verifies that
max_tokens calculation is handled by prompt_manager.
"""

import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_max_tokens_calculation():
    """Test that max_tokens is calculated by prompt_manager."""
    print("🧪 Testing max_tokens calculation by prompt_manager")
    print("=" * 50)

    try:
        from models.llamacpp_provider import LlamaCppChatModel
        from models.prompt_manager import PromptManager
        import config

        # Create LlamaCppChatModel instance
        llm = LlamaCppChatModel(api_url = config.LLAMACPP_API_URL)

        # Verify prompt_manager is properly initialized
        assert hasattr(llm, 'prompt_manager'), "LLM should have prompt_manager attribute"
        assert isinstance(llm.prompt_manager, PromptManager), "prompt_manager should be PromptManager instance"

        print(f"✅ LlamaCppChatModel created with prompt_manager")
        print(f"   - max_context_tokens: {llm.prompt_manager.max_context_tokens}")
        print(f"   - reserved_tokens: {llm.prompt_manager.reserved_tokens}")
        print(f"   - default_max_tokens: {llm.prompt_manager.default_max_tokens}")

        # Test prompt_manager methods
        test_prompt = "Hello, world! This is a test prompt."

        # Test token counting
        token_count = llm.prompt_manager.count_tokens(test_prompt)
        print(f"✅ Token counting works: '{test_prompt}' = {token_count} tokens")

        # Test optimal max_tokens calculation (no specific request)
        optimal_tokens = llm.prompt_manager.calculate_optimal_max_tokens(test_prompt)
        print(f"✅ Optimal max_tokens calculation: {optimal_tokens} tokens")

        # Test optimal max_tokens calculation (with specific request)
        requested_tokens = 100
        optimal_tokens_requested = llm.prompt_manager.calculate_optimal_max_tokens(test_prompt, requested_tokens)
        print(f"✅ Optimal max_tokens with request ({requested_tokens}): {optimal_tokens_requested} tokens")

        # Verify that optimal tokens don't exceed available space
        prompt_tokens = llm.prompt_manager.count_tokens(test_prompt)
        available_tokens = llm.prompt_manager.max_context_tokens - prompt_tokens - llm.prompt_manager.reserved_tokens
        assert optimal_tokens <= available_tokens, f"Optimal tokens ({optimal_tokens}) should not exceed available ({available_tokens})"
        assert optimal_tokens_requested <= min(requested_tokens,
                                               available_tokens), f"Requested optimal tokens should respect limits"

        print(f"✅ Token limits respected:")
        print(f"   - Prompt tokens: {prompt_tokens}")
        print(f"   - Available tokens: {available_tokens}")
        print(f"   - Optimal tokens (default): {optimal_tokens}")
        print(f"   - Optimal tokens (requested): {optimal_tokens_requested}")

        # Test backward compatibility
        adjusted_tokens = llm.prompt_manager.adjust_max_tokens(test_prompt, 150)
        print(f"✅ Backward compatibility (adjust_max_tokens): {adjusted_tokens} tokens")

        # Verify that no hardcoded max_tokens exists in LlamaCppChatModel
        assert not hasattr(llm, 'max_tokens') or not hasattr(type(llm),
                                                             'max_tokens'), "LlamaCppChatModel should not have hardcoded max_tokens"

        print("\n🎉 All max_tokens calculation tests passed!")
        print("✅ max_tokens is now ALWAYS calculated by prompt_manager.py")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_deprecation():
    """Test that config no longer includes max_tokens in model config."""
    print("\n🧪 Testing config deprecation")
    print("=" * 30)

    try:
        import config

        # Test that get_model_config no longer includes max_tokens
        model_config = config.get_model_config()

        assert "max_tokens" not in model_config, "max_tokens should be removed from model config"
        print("✅ max_tokens removed from model config")

        # Verify that LLAMACPP_MAX_TOKENS still exists for backward compatibility
        assert hasattr(config, 'LLAMACPP_MAX_TOKENS'), "LLAMACPP_MAX_TOKENS should exist for backward compatibility"
        print("✅ LLAMACPP_MAX_TOKENS preserved for backward compatibility")

        # Verify required config values exist
        required_keys = ['provider', 'name', 'api_url']
        for key in required_keys:
            assert key in model_config, f"Required key '{key}' missing from model config"

        print("✅ Model config contains required keys:", list(model_config.keys()))
        return True

    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False


def main():
    """Run all max_tokens calculation tests."""
    print("🧪 Running max_tokens Calculation Tests")
    print("=" * 60)

    success = True

    # Test max_tokens calculation
    if not test_max_tokens_calculation():
        success = False

    # Test config deprecation
    if not test_config_deprecation():
        success = False

    print("\n" + "=" * 60)
    if success:
        print("🎉 All max_tokens calculation tests passed!")
        print("✅ Task completed: max_tokens is now ALWAYS calculated by prompt_manager.py")
        print("\n📋 Summary of changes:")
        print("   • PromptManager now has calculate_optimal_max_tokens() method")
        print("   • LlamaCppChatModel no longer has hardcoded max_tokens")
        print("   • LlamaCppProvider doesn't pass max_tokens from config")
        print("   • Config deprecates LLAMACPP_MAX_TOKENS (kept for compatibility)")
        print("   • All max_tokens calculations go through prompt_manager")
    else:
        print("❌ Some tests failed. Please check the errors above.")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
