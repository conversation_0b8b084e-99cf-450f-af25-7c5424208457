#!/usr/bin/env python3
"""
Test for the reworked summarization_node.

This test verifies that the enhanced summarization functionality works correctly,
including the new SummaryManager class and improved summarization logic.
"""

import os
import sys
import unittest
from unittest.mock import patch, Mock

from langchain.schema.messages import HumanMessage, AIMessage

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class TestSummarizationRework(unittest.TestCase):
    """Test the reworked summarization functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_llm = Mock()
        self.mock_llm.prompt_manager.count_tokens.return_value = 10
        self.mock_llm.prompt_manager.calculate_optimal_max_tokens.return_value = 150

        # Mock response for summarization
        self.mock_response = Mock()
        self.mock_response.content = "Summary: User asked about math, assistant provided calculations."
        self.mock_llm.invoke.return_value = self.mock_response

    def test_summary_manager_creation(self):
        """Test that SummaryManager can be created."""
        from core.nodes.summarization_node import SummaryManager

        manager = SummaryManager()
        self.assertIsNotNone(manager)
        self.assertIsNotNone(manager.logger)
        print("✅ SummaryManager creation test passed")

    def test_should_summarize_logic(self):
        """Test the enhanced should_summarize logic."""
        from core.nodes.summarization_node import SummaryManager
        from core.state import AgentState

        manager = SummaryManager()

        # Test: Below threshold - should not summarize
        state = AgentState(
                messages = [HumanMessage(content = "Hello"), AIMessage(content = "Hi")],
                window_size = 5
        )
        self.assertFalse(manager.should_summarize(state))

        # Test: Above threshold - should summarize
        messages = []
        for i in range(7):  # More than window_size of 5
            messages.append(HumanMessage(content = f"Message {i}"))
            messages.append(AIMessage(content = f"Response {i}"))

        state = AgentState(messages = messages, window_size = 5)
        self.assertTrue(manager.should_summarize(state))

        # Test: Recent summary exists - should not over-summarize
        # Create state with just slightly more than window_size
        state = AgentState(
                messages = messages[:6],  # 6 messages (window_size + 1)
                window_size = 5,
                summary = "Recent summary exists"
        )
        self.assertFalse(manager.should_summarize(state))

        print("✅ Should summarize logic test passed")

    def test_extract_conversation_text(self):
        """Test conversation text extraction."""
        from core.nodes.summarization_node import SummaryManager

        manager = SummaryManager()

        messages = [
                HumanMessage(content = "Hello"),
                AIMessage(content = "Hi there!"),
                HumanMessage(content = "How are you?"),
                AIMessage(content = "I'm doing well, thanks!")
        ]

        # Test without existing summary
        text = manager.extract_conversation_text(messages, include_summary = False)
        expected_lines = [
                "Human: Hello",
                "Assistant: Hi there!",
                "Human: How are you?",
                "Assistant: I'm doing well, thanks!"
        ]
        self.assertEqual(text, "\n".join(expected_lines))

        # Test with existing summary
        existing_summary = "Previous conversation about greetings"
        text_with_summary = manager.extract_conversation_text(
                messages, include_summary = True, existing_summary = existing_summary
        )
        expected_with_summary = [
                                        "Previous Summary: Previous conversation about greetings",
                                        "---"
                                ] + expected_lines
        self.assertEqual(text_with_summary, "\n".join(expected_with_summary))

        print("✅ Extract conversation text test passed")

    def test_create_summary_prompt(self):
        """Test summary prompt creation."""
        from core.nodes.summarization_node import SummaryManager

        manager = SummaryManager()

        conversation_text = "Human: Hello\nAssistant: Hi there!"

        # Test new summary prompt
        prompt = manager.create_summary_prompt(conversation_text)
        self.assertIn("expert conversation summarizer", prompt)
        self.assertIn("Key facts", prompt)
        self.assertIn(conversation_text, prompt)
        self.assertNotIn("Update the existing summary", prompt)

        # Test update summary prompt
        existing_summary = "Previous conversation summary"
        update_prompt = manager.create_summary_prompt(conversation_text, existing_summary)
        self.assertIn("Update the existing summary", update_prompt)
        self.assertIn("Merge new information", update_prompt)
        self.assertIn(conversation_text, update_prompt)

        print("✅ Create summary prompt test passed")

    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_node_no_trigger(self, mock_get_llm):
        """Test summarization node when summarization is not triggered."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        mock_get_llm.return_value = self.mock_llm

        # Create state with few messages (below threshold)
        state = AgentState(
                messages = [
                        HumanMessage(content = "Hello"),
                        AIMessage(content = "Hi there!")
                ],
                window_size = 5
        )

        result = summarization_node(state)

        # Should not trigger summarization
        self.assertEqual(len(result["messages"]), 2)
        self.assertNotIn("summary", result)
        # LLM should not be called
        self.mock_llm.invoke.assert_not_called()

        print("✅ Summarization node (no trigger) test passed")

    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_node_with_trigger(self, mock_get_llm):
        """Test summarization node when summarization is triggered."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        mock_get_llm.return_value = self.mock_llm

        # Create state with many messages (above threshold)
        messages = []
        for i in range(7):  # More than window_size of 3
            messages.append(HumanMessage(content = f"Message {i}"))
            messages.append(AIMessage(content = f"Response {i}"))

        state = AgentState(
                messages = messages,
                window_size = 3
        )

        result = summarization_node(state)

        # Should trigger summarization
        self.assertIn("summary", result)
        self.assertEqual(len(result["messages"]), 3)  # Pruned to window_size
        self.assertIn("Summary:", result["summary"])

        # Verify LLM was called with enhanced prompt
        self.mock_llm.invoke.assert_called_once()
        call_args = self.mock_llm.invoke.call_args
        messages_sent = call_args[0][0]

        # Check that system message is enhanced
        system_msg = messages_sent[0]
        self.assertIn("expert conversation summarizer", system_msg.content)

        # Check that prompt includes enhanced instructions
        human_msg = messages_sent[1]
        self.assertIn("Key facts", human_msg.content)
        self.assertIn("User preferences", human_msg.content)

        print("✅ Summarization node (with trigger) test passed")

    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_node_with_existing_summary(self, mock_get_llm):
        """Test summarization node with existing summary."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        mock_get_llm.return_value = self.mock_llm

        # Create state with existing summary and many messages
        messages = []
        for i in range(8):  # More than window_size
            messages.append(HumanMessage(content = f"New message {i}"))
            messages.append(AIMessage(content = f"New response {i}"))

        state = AgentState(
                messages = messages,
                window_size = 3,
                summary = "Existing conversation summary about previous topics"
        )

        result = summarization_node(state)

        # Should trigger summarization with update prompt
        self.assertIn("summary", result)
        self.assertEqual(len(result["messages"]), 3)

        # Verify LLM was called with update prompt
        call_args = self.mock_llm.invoke.call_args
        messages_sent = call_args[0][0]
        human_msg = messages_sent[1]

        # Should include existing summary and update instructions
        self.assertIn("Previous Summary:", human_msg.content)
        self.assertIn("Existing conversation summary", human_msg.content)
        self.assertIn("Update the existing summary", human_msg.content)

        print("✅ Summarization node (with existing summary) test passed")

    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_node_error_handling(self, mock_get_llm):
        """Test summarization node error handling."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        # Mock LLM that raises an exception
        error_llm = Mock()
        error_llm.prompt_manager.count_tokens.return_value = 10
        error_llm.prompt_manager.calculate_optimal_max_tokens.return_value = 150
        error_llm.invoke.side_effect = Exception("Mock LLM error")
        mock_get_llm.return_value = error_llm

        # Create state that would trigger summarization
        messages = []
        for i in range(7):
            messages.append(HumanMessage(content = f"Message {i}"))
            messages.append(AIMessage(content = f"Response {i}"))

        state = AgentState(
                messages = messages,
                window_size = 3,
                summary = "Existing summary"
        )

        # Should handle error gracefully
        result = summarization_node(state)

        # Should still prune messages even on error
        self.assertEqual(len(result["messages"]), 3)
        # Should keep existing summary on error
        self.assertEqual(result.get("summary"), "Existing summary")

        print("✅ Summarization node error handling test passed")

    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_node_empty_response(self, mock_get_llm):
        """Test summarization node with empty LLM response."""
        from core.nodes.summarization_node import summarization_node
        from core.state import AgentState

        # Mock LLM that returns empty response
        empty_llm = Mock()
        empty_llm.prompt_manager.count_tokens.return_value = 10
        empty_llm.prompt_manager.calculate_optimal_max_tokens.return_value = 150
        empty_response = Mock()
        empty_response.content = ""
        empty_llm.invoke.return_value = empty_response
        mock_get_llm.return_value = empty_llm

        messages = []
        for i in range(7):
            messages.append(HumanMessage(content = f"Message {i}"))
            messages.append(AIMessage(content = f"Response {i}"))

        state = AgentState(
                messages = messages,
                window_size = 3,
                summary = "Existing summary"
        )

        result = summarization_node(state)

        # Should keep existing summary when LLM returns empty
        self.assertEqual(result.get("summary"), "Existing summary")
        self.assertEqual(len(result["messages"]), 3)

        print("✅ Summarization node empty response test passed")


def main():
    """Run the summarization rework tests."""
    print("🧪 Running Summarization Rework Tests")
    print("=" * 50)

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSummarizationRework)

    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity = 2)
    result = runner.run(suite)

    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All summarization rework tests passed!")
        print("✅ Enhanced summarization functionality is working correctly.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print(f"Failed: {len(result.failures)}, Errors: {len(result.errors)}")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
