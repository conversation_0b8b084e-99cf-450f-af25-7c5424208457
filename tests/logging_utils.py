#!/usr/bin/env python3
"""
Model-specific logging utilities for LLM testing framework.

Provides comprehensive logging of prompts, responses, and test results
with separate log files for each model being tested.
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import tiktoken


class ModelTestLogger:
    """
    Model-specific logger for LLM testing framework.
    
    Creates separate log files for each model with detailed prompt/response logging.
    """

    def __init__(self, model_name: str, test_type: str = "general", log_dir: str = None):
        """
        Initialize model-specific logger.

        Args:
            model_name: Name of the model being tested
            test_type: Type of test being run (e.g., "comparison", "complexity", "benchmarking")
            log_dir: Directory to store log files
        """
        self.model_name = self._sanitize_model_name(model_name)
        self.test_type = test_type

        # Set default log directory relative to tests package
        if log_dir is None:
            # Get the directory where this file is located (tests package)
            tests_dir = Path(__file__).parent
            log_dir = tests_dir / "test_logs"

        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok = True)

        # Create timestamp for this test session
        self.session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Setup log file paths with cleaner format
        if test_type.startswith("test_"):
            # For unified tests, use the provided timestamp format
            self.log_filename = f"{self.model_name}_{test_type}.log"
            self.json_filename = f"{self.model_name}_{test_type}.json"
        else:
            # For other tests, use the original format
            self.log_filename = f"{self.model_name}_{test_type}_{self.session_timestamp}.log"
            self.json_filename = f"{self.model_name}_{test_type}_{self.session_timestamp}.json"
        self.log_filepath = self.log_dir / self.log_filename
        self.json_filepath = self.log_dir / self.json_filename

        # Initialize tiktoken tokenizer for accurate token counting
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        except Exception:
            # Fallback if tiktoken fails
            self.tokenizer = None

        # Setup logger
        self.logger = self._setup_logger()

        # Initialize session data
        self.session_data = {
                "model_name"   : model_name,
                "test_type"    : test_type,
                "session_start": datetime.now().isoformat(),
                "test_results" : [],
                "summary"      : {}
        }

        # Log session start
        self.log_session_start()

    def _sanitize_model_name(self, model_name: str) -> str:
        """Sanitize model name for use in filenames."""
        # Replace invalid filename characters
        invalid_chars = '<>:"/\\|?*'
        sanitized = model_name
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')

        # Replace spaces and other problematic characters
        sanitized = sanitized.replace(' ', '_').replace('-', '_')

        return sanitized

    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        if self.tokenizer is None:
            # Fallback: rough estimation (4 chars ≈ 1 token)
            return len(text) // 4

        try:
            tokens = self.tokenizer.encode(text)
            return len(tokens)
        except Exception:
            # Fallback if tokenization fails
            return len(text) // 4

    def _setup_logger(self) -> logging.Logger:
        """Setup dedicated logger for this model."""
        logger_name = f"model_test_{self.model_name}_{self.test_type}"
        logger = logging.getLogger(logger_name)

        # Clear any existing handlers
        logger.handlers.clear()
        logger.setLevel(logging.INFO)

        # Create file handler
        file_handler = logging.FileHandler(self.log_filepath, mode = 'w', encoding = 'utf-8')
        file_handler.setLevel(logging.INFO)

        # Create formatter
        formatter = logging.Formatter(
                '%(asctime)s | %(levelname)s | %(message)s',
                datefmt = '%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)

        logger.addHandler(file_handler)

        # Prevent propagation to root logger
        logger.propagate = False

        return logger

    def log_session_start(self):
        """Log the start of a testing session."""
        self.logger.info("=" * 80)
        self.logger.info(f"MODEL TESTING SESSION STARTED")
        self.logger.info(f"Model: {self.model_name}")
        self.logger.info(f"Test Type: {self.test_type}")
        self.logger.info(f"Session ID: {self.session_timestamp}")
        self.logger.info(f"Log File: {self.log_filename}")
        self.logger.info("=" * 80)

    def log_test_start(self, test_name: str, category: str, complexity: int = 1, run_number: int = 1):
        """Log the start of an individual test."""
        self.logger.info("")
        self.logger.info("-" * 60)
        self.logger.info(f"TEST START: {test_name}")
        self.logger.info(f"Category: {category}")
        self.logger.info(f"Complexity Level: {complexity}")
        self.logger.info(f"Run Number: {run_number}")
        self.logger.info("-" * 60)

    def log_prompt(self, prompt: str, test_name: str, llm_params: Dict[str, Any] = None):
        """Log the prompt sent to the model with enhanced token details."""
        prompt_tokens = self.count_tokens(prompt)

        self.logger.info("PROMPT DETAILS:")
        self.logger.info(f"  Test Name: {test_name}")
        self.logger.info(f"  Prompt Tokens: {prompt_tokens}")
        self.logger.info(f"  Has Newlines: {'Yes' if chr(10) in prompt else 'No'}")

        # Enhanced token information logging
        if llm_params:
            self.logger.info("TOKEN ALLOCATION:")
            if "max_tokens_requested" in llm_params:
                requested = llm_params['max_tokens_requested']
                if llm_params.get("was_auto_tokens", False):
                    self.logger.info(f"  Requested Max Tokens: AUTO → {requested} (prompt_manager recommendation)")
                else:
                    self.logger.info(f"  Requested Max Tokens: {requested}")
            if "max_tokens_available" in llm_params:
                self.logger.info(
                        f"  Available Max Tokens: {llm_params['max_tokens_available']} (via adjust_max_tokens)")
            if "max_tokens_used" in llm_params:
                self.logger.info(f"  Used Max Tokens: {llm_params['max_tokens_used']}")

            # Show prompt_manager usage
            if "prompt_manager_used" in llm_params:
                if llm_params["prompt_manager_used"]:
                    self.logger.info(f"  Prompt Manager: ✅ ACTIVE (dynamic token adjustment)")
                else:
                    self.logger.info(f"  Prompt Manager: ❌ FALLBACK (static calculation)")

            # Show token efficiency
            if "max_tokens_available" in llm_params and "max_tokens_used" in llm_params:
                efficiency = (llm_params["max_tokens_used"] / llm_params["max_tokens_available"]) * 100
                self.logger.info(f"  Token Efficiency: {efficiency:.1f}%")

            self.logger.info("LLM PARAMETERS:")
            for key, value in llm_params.items():
                if not key.startswith("max_tokens"):  # Already logged above
                    self.logger.info(f"  {key}: {value}")

        self.logger.info("-" * 60)
        self.logger.info("FULL PROMPT:")
        self.logger.info("-" * 60)
        self.logger.info(prompt)
        self.logger.info("-" * 60)

    def log_response(self, response: str, latency_ms: float, test_name: str, success: bool = None,
                     found_patterns: list = None, missing_patterns: list = None,
                     llm_params: Dict[str, Any] = None):
        """Log the model's response and timing with enhanced analysis - MERGED with test results."""
        response_tokens = self.count_tokens(response)
        has_numbers = any(char.isdigit() for char in response)
        has_newlines = '\n' in response

        # Contamination detection based on tokens
        contamination_flags = []
        if response_tokens > 200:
            contamination_flags.append("Very long response")
        # Don't flag numbers in math, calculation, finance, or question tests
        if (has_numbers and
                "math" not in test_name.lower() and
                "calculate" not in test_name.lower() and
                "question" not in test_name.lower() and
                "finance" not in test_name.lower() and
                "interest" not in test_name.lower() and
                "comparison" not in test_name.lower()):
            contamination_flags.append("Unexpected numbers")
        if response.count('\n') > 5:
            contamination_flags.append("Many line breaks")
        if any(str(i) + '.' in response for i in range(1, 20)):
            contamination_flags.append("Numbered list format")

        # This is now the MERGED log entry combining response details and test results
        self.logger.info("TEST RESULTS:")
        self.logger.info(f"  Test Name: {test_name}")
        self.logger.info(f"  Success: {success if success is not None else 'Unknown'}")
        self.logger.info(f"  Latency: {latency_ms:.0f}ms")
        self.logger.info(f"  Response Tokens: {response_tokens}")
        self.logger.info(f"  Has Numbers: {'Yes' if has_numbers else 'No'}")
        self.logger.info(f"  Has Newlines: {'Yes' if has_newlines else 'No'}")

        # Log pattern matching results
        if found_patterns:
            self.logger.info(f"  Found Patterns: {', '.join(found_patterns)}")
        if missing_patterns:
            self.logger.info(f"  Missing Patterns: {', '.join(missing_patterns)}")

        if contamination_flags:
            self.logger.warning(f"  CONTAMINATION FLAGS: {', '.join(contamination_flags)}")
        else:
            self.logger.info("  CONTAMINATION FLAGS: None detected")

        self.logger.info("-" * 60)
        self.logger.info("FULL RESPONSE:")
        self.logger.info("-" * 60)
        self.logger.info(response)
        self.logger.info("-" * 60)
        self.logger.info("RESPONSE REPR (shows exact characters):")
        self.logger.info(repr(response))
        self.logger.info("-" * 60)

    def store_test_result(self, test_result: Dict[str, Any]):
        """Store test result in session data with enhanced token information and new measurements."""
        # Add to session data with enhanced token information (removed irrelevant entries that are always 0)
        result_copy = {
                'timestamp'              : datetime.now().isoformat(),
                'name'                   : test_result.get('name', ''),
                'category'               : test_result.get('category', ''),
                'test_type'              : test_result.get('test_type', ''),
                'complexity_level'       : test_result.get('complexity_level', 1),
                'llm_params'             : test_result.get('llm_params', {}),
                'success'                : test_result.get('success', False),
                'latency_ms'             : test_result.get('latency_ms', 0.0),
                'latency_per_token'      : test_result.get('latency_per_token', 0.0),
                'prompt_tokens'          : test_result.get('prompt_tokens', 0),
                'response_tokens'        : test_result.get('response_tokens', 0),
                'total_tokens'           : test_result.get('total_tokens', 0),
                'max_tokens_requested'   : test_result.get('max_tokens_requested', 0),
                'max_tokens_available'   : test_result.get('max_tokens_available', 0),
                'max_tokens_used'        : test_result.get('max_tokens_used', 0),
                'prompt_manager_used'    : test_result.get('prompt_manager_used', False),
                'found_patterns'         : list(test_result.get('found_patterns', [])),
                'missing_patterns'       : list(test_result.get('missing_patterns', [])),
                'found_unwanted_patterns': list(test_result.get('found_unwanted_patterns', [])),
                'score_wanted_patterns'  : test_result.get('score_wanted_patterns', 0.0),
                'score_unwanted_patterns': test_result.get('score_unwanted_patterns', 0.0),
                'score_looping'          : test_result.get('score_looping', 0.0),
                'error'                  : test_result.get('error')
        }
        self.session_data["test_results"].append(result_copy)

    def log_consistency_analysis(self, test_name: str, runs_data: list):
        """Log consistency analysis across multiple runs using new measurements."""
        if len(runs_data) <= 1:
            return

        self.logger.info("")
        self.logger.info("CONSISTENCY ANALYSIS:")

        # Calculate statistics using new measurements
        wanted_scores = [r.get('score_wanted_patterns', 0.0) for r in runs_data if r.get('success')]
        unwanted_scores = [r.get('score_unwanted_patterns', 0.0) for r in runs_data if r.get('success')]
        looping_scores = [r.get('score_looping', 0.0) for r in runs_data if r.get('success')]
        latencies = [r.get('latency_ms', 0.0) for r in runs_data if r.get('success')]
        latency_per_token = [r.get('latency_per_token', 0.0) for r in runs_data if r.get('success')]

        if wanted_scores:
            import statistics
            wanted_mean = statistics.mean(wanted_scores)
            wanted_stdev = statistics.stdev(wanted_scores) if len(wanted_scores) > 1 else 0.0
            unwanted_mean = statistics.mean(unwanted_scores)
            looping_mean = statistics.mean(looping_scores)
            lat_mean = statistics.mean(latencies)
            lat_stdev = statistics.stdev(latencies) if len(latencies) > 1 else 0.0
            lat_per_token_mean = statistics.mean(latency_per_token) if latency_per_token else 0.0

            self.logger.info(f"  Wanted Patterns Score - Mean: {wanted_mean:.3f}, StdDev: {wanted_stdev:.3f}")
            self.logger.info(f"  Unwanted Patterns Score - Mean: {unwanted_mean:.3f}")
            self.logger.info(f"  Looping Score - Mean: {looping_mean:.3f}")
            self.logger.info(f"  Latency - Mean: {lat_mean:.1f}ms, StdDev: {lat_stdev:.1f}ms")
            self.logger.info(f"  Latency per Token - Mean: {lat_per_token_mean:.2f}ms/token")
            self.logger.info(f"  Consistency Score: {1.0 - wanted_stdev:.3f}")

    def log_category_summary(self, category: str, category_results: list):
        """Log summary for a test category."""
        successful_tests = [r for r in category_results if r.get('success')]
        total_tests = len(category_results)
        success_rate = len(successful_tests) / total_tests if total_tests > 0 else 0.0

        self.logger.info("")
        self.logger.info("=" * 60)
        self.logger.info(f"CATEGORY SUMMARY: {category}")
        self.logger.info("=" * 60)
        self.logger.info(f"Success Rate: {len(successful_tests)}/{total_tests} ({success_rate:.1%})")

        if successful_tests:
            import statistics
            avg_wanted_score = statistics.mean([r.get('score_wanted_patterns', 0.0) for r in successful_tests])
            avg_unwanted_score = statistics.mean([r.get('score_unwanted_patterns', 0.0) for r in successful_tests])
            avg_looping_score = statistics.mean([r.get('score_looping', 0.0) for r in successful_tests])
            avg_latency = statistics.mean([r.get('latency_ms', 0.0) for r in successful_tests])
            avg_latency_per_token = statistics.mean(
                    [r.get('latency_per_token', 0.0) for r in successful_tests if r.get('latency_per_token', 0.0) > 0])

            self.logger.info(f"Average Wanted Patterns Score: {avg_wanted_score:.3f}")
            self.logger.info(f"Average Unwanted Patterns Score: {avg_unwanted_score:.3f}")
            self.logger.info(f"Average Looping Score: {avg_looping_score:.3f}")
            self.logger.info(f"Average Latency: {avg_latency:.1f}ms")
            if avg_latency_per_token > 0:
                self.logger.info(f"Average Latency per Token: {avg_latency_per_token:.2f}ms/token")

    def log_session_summary(self, summary_data: Dict[str, Any]):
        """Log final session summary."""
        self.logger.info("")
        self.logger.info("=" * 80)
        self.logger.info("SESSION SUMMARY")
        self.logger.info("=" * 80)

        self.logger.info(f"Model: {self.model_name}")
        self.logger.info(f"Test Type: {self.test_type}")
        self.logger.info(f"Total Tests: {summary_data.get('total_tests', 0)}")
        self.logger.info(f"Successful Tests: {summary_data.get('successful_tests', 0)}")
        self.logger.info(f"Failed Tests: {summary_data.get('failed_tests', 0)}")
        self.logger.info(f"Overall Score: {summary_data.get('overall_score', 0.0):.3f}")

        # Log performance metrics
        metrics = summary_data.get('metrics', {})
        if metrics:
            self.logger.info("")
            self.logger.info("PERFORMANCE METRICS:")
            self.logger.info(f"  Average Accuracy: {metrics.get('accuracy', 0.0):.3f}")
            self.logger.info(f"  Instruction Following: {metrics.get('instruction_following', 0.0):.3f}")
            self.logger.info(f"  Consistency: {metrics.get('consistency', 0.0):.3f}")
            self.logger.info(f"  Average Latency: {metrics.get('latency_ms', 0.0):.1f}ms")
            self.logger.info(f"  Median Latency: {metrics.get('median_latency_ms', 0.0):.1f}ms")

        # Log category scores
        category_scores = summary_data.get('category_scores', {})
        if category_scores:
            self.logger.info("")
            self.logger.info("CATEGORY SCORES:")
            for category, score in category_scores.items():
                self.logger.info(f"  {category}: {score:.3f}")

        # Update session data
        self.session_data["summary"] = summary_data
        self.session_data["session_end"] = datetime.now().isoformat()

        self.logger.info("")
        self.logger.info("=" * 80)
        self.logger.info("SESSION COMPLETED")
        self.logger.info(f"Log saved to: {self.log_filepath}")
        self.logger.info(f"JSON data saved to: {self.json_filepath}")
        self.logger.info("=" * 80)

    def log_test_error(self, test_name: str, error_msg: str):
        """Log test error details."""
        self.logger.error("")
        self.logger.error("❌ TEST ERROR")
        self.logger.error(f"Test Name: {test_name}")
        self.logger.error(f"Error: {error_msg}")
        self.logger.error("")

    # Removed duplicate log_test_result method - now using log_response for unified logging

    def save_json_data(self, data = None):
        """Save session data to JSON file."""
        try:
            # Use provided data or default to session_data
            data_to_save = data if data is not None else self.session_data
            with open(self.json_filepath, 'w', encoding = 'utf-8') as f:
                json.dump(data_to_save, f, indent = 2, ensure_ascii = False)
        except Exception as e:
            self.logger.error(f"Failed to save JSON data: {e}")

    def close(self):
        """Close the logger and save final data."""
        self.save_json_data()

        # Close all handlers
        for handler in self.logger.handlers:
            handler.close()

        # Clear handlers
        self.logger.handlers.clear()


def get_model_logger(model_name: str, test_type: str = "general") -> ModelTestLogger:
    """
    Factory function to create a model-specific logger.
    
    Args:
        model_name: Name of the model being tested
        test_type: Type of test being run
        
    Returns:
        ModelTestLogger instance
    """
    return ModelTestLogger(model_name, test_type)

# Log file management functions removed - user prefers manual log viewing
