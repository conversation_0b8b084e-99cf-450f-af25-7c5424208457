#!/usr/bin/env python3
"""
Mock LLM test for DaBot Agent LangGraph Edition.

This test verifies that the agent works correctly with a mock LLM,
testing the complete conversation flow without requiring an actual
LLM server connection.
"""

import os
import sys
import unittest
from unittest.mock import patch, Mock

from langchain.schema.messages import HumanMessage

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class MockLLMResponse:
    """Mock LLM response object."""

    def __init__(self, content: str):
        self.content = content


class MockLLM:
    """Mock LLM for testing."""

    def __init__(self, responses = None):
        self.responses = responses or ["I'm a mock LLM response."]
        self.call_count = 0
        self.prompt_manager = Mock()
        self.prompt_manager.count_tokens.return_value = 10
        self.prompt_manager.max_context_tokens = 2048
        self.prompt_manager.reserved_tokens = 50
        self.prompt_manager.adjust_max_tokens.return_value = 200
        self.prompt_manager.calculate_optimal_max_tokens.return_value = 200

    def invoke(self, messages):
        """Mock invoke method."""
        response = self.responses[self.call_count % len(self.responses)]
        self.call_count += 1
        return MockLLMResponse(response)


class TestAgentWithMock(unittest.TestCase):
    """Test agent functionality with mock LLM."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_llm = MockLLM([
                "Hello! I'm a helpful AI assistant.",
                "2 + 2 equals 4.",
                "The square root of 16 is 4.",
                "I can help you with various tasks."
        ])

    @patch('core.nodes.llm_node.get_cached_llm')
    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_simple_conversation(self, mock_sum_llm, mock_llm_llm):
        """Test a simple conversation flow with mock LLM."""
        from core.graphs.orchestrator import AgentOrchestrator

        # Set up mocks
        mock_llm_llm.return_value = self.mock_llm
        mock_sum_llm.return_value = self.mock_llm

        orchestrator = AgentOrchestrator()

        # Test simple question
        result = orchestrator.run_conversation("Hello, how are you?")

        self.assertIn("final_answer", result)
        self.assertIsInstance(result["final_answer"], str)
        self.assertGreater(len(result["final_answer"]), 0)
        print(f"✅ Simple conversation test passed. Response: {result['final_answer']}")

    @patch('core.nodes.llm_node.get_cached_llm')
    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_math_conversation(self, mock_sum_llm, mock_llm_llm):
        """Test a math-related conversation with mock LLM."""
        from core.graphs.orchestrator import AgentOrchestrator

        # Set up mock with math-specific responses
        math_llm = MockLLM([
                "2 + 2 equals 4.",
                "The square root of 16 is 4.",
                "10 * 5 equals 50."
        ])
        mock_llm_llm.return_value = math_llm
        mock_sum_llm.return_value = math_llm

        orchestrator = AgentOrchestrator()

        # Test math question
        result = orchestrator.run_conversation("What is 2 + 2?")

        self.assertIn("final_answer", result)
        self.assertIn("4", result["final_answer"])
        print(f"✅ Math conversation test passed. Response: {result['final_answer']}")

    @patch('core.nodes.llm_node.get_cached_llm')
    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_conversation_with_history(self, mock_sum_llm, mock_llm_llm):
        """Test conversation with message history."""
        from core.graphs.orchestrator import AgentOrchestrator

        # Set up mocks
        mock_llm_llm.return_value = self.mock_llm
        mock_sum_llm.return_value = self.mock_llm

        orchestrator = AgentOrchestrator()

        # Test conversation - the orchestrator manages history internally
        result = orchestrator.run_conversation("Can you tell me a joke?")

        self.assertIn("final_answer", result)
        self.assertIn("thread_id", result)
        self.assertIn("success", result)
        # Check that the result structure is correct
        self.assertTrue(result["success"])
        print(f"✅ Conversation with history test passed. Thread ID: {result['thread_id']}")

    @patch('core.nodes.llm_node.get_cached_llm')
    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_summarization_trigger(self, mock_sum_llm, mock_llm_llm):
        """Test that summarization is triggered with long conversations."""
        from core.graphs.orchestrator import AgentOrchestrator

        # Set up mock with summarization response
        summary_llm = MockLLM([
                "Summary: User asked multiple questions about math and general topics.",
                "The user is asking follow-up questions."
        ])
        mock_llm_llm.return_value = summary_llm
        mock_sum_llm.return_value = summary_llm

        orchestrator = AgentOrchestrator()

        # Test conversation - summarization is handled internally by the graph
        result = orchestrator.run_conversation("Final question")

        self.assertIn("final_answer", result)
        self.assertIn("success", result)
        self.assertTrue(result["success"])
        # The orchestrator manages summarization internally
        print(f"✅ Summarization trigger test passed. Success: {result['success']}")

    @patch('core.nodes.llm_node.get_cached_llm')
    def test_error_handling(self, mock_llm_llm):
        """Test error handling in the agent."""
        from core.nodes.llm_node import llm_node
        from core.state import AgentState

        # Mock LLM that raises an exception
        error_llm = Mock()
        error_llm.invoke.side_effect = Exception("Mock LLM error")
        error_llm.prompt_manager.count_tokens.return_value = 10
        error_llm.prompt_manager.max_context_tokens = 2048
        error_llm.prompt_manager.reserved_tokens = 50
        mock_llm_llm.return_value = error_llm

        state = AgentState(
                user_input = "Test question",
                messages = [HumanMessage(content = "Test question")]
        )

        # Should handle the error gracefully
        try:
            result = llm_node(state)
            # If no exception is raised, the error was handled
            print("✅ Error handling test passed - error was caught and handled")
        except Exception as e:
            # If an exception is raised, check if it's properly handled
            self.assertIn("error", str(e).lower())
            print("✅ Error handling test passed - error was properly propagated")

    @patch('core.nodes.llm_node.get_cached_llm')
    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_graph_streaming(self, mock_sum_llm, mock_llm_llm):
        """Test graph streaming functionality using the underlying graph."""
        from core.graphs.orchestrator import AgentOrchestrator
        from core.state import create_initial_state

        # Set up mocks
        mock_llm_llm.return_value = self.mock_llm
        mock_sum_llm.return_value = self.mock_llm

        orchestrator = AgentOrchestrator()

        # Test streaming using the graph directly
        initial_state = create_initial_state("Tell me about AI")
        config = {"configurable": {"thread_id": "test-thread"}}

        steps = list(orchestrator.graph.stream(initial_state, config))

        self.assertGreater(len(steps), 0)
        # Should have at least one step
        self.assertIsInstance(steps, list)
        print(f"✅ Graph streaming test passed. Steps: {len(steps)}")

    @patch('core.nodes.llm_node.get_cached_llm')
    @patch('core.nodes.summarization_node.get_cached_llm')
    def test_empty_input_handling(self, mock_sum_llm, mock_llm_llm):
        """Test handling of empty or invalid input."""
        from core.graphs.orchestrator import AgentOrchestrator

        # Set up mocks
        mock_llm_llm.return_value = self.mock_llm
        mock_sum_llm.return_value = self.mock_llm

        orchestrator = AgentOrchestrator()

        # Test empty input
        result = orchestrator.run_conversation("")
        self.assertIn("final_answer", result)

        # Test whitespace-only input
        result = orchestrator.run_conversation("   ")
        self.assertIn("final_answer", result)

        print("✅ Empty input handling test passed")

    def test_mock_llm_functionality(self):
        """Test the mock LLM itself."""
        mock = MockLLM(["Response 1", "Response 2"])

        # Test first call
        response1 = mock.invoke([])
        self.assertEqual(response1.content, "Response 1")

        # Test second call
        response2 = mock.invoke([])
        self.assertEqual(response2.content, "Response 2")

        # Test cycling back to first response
        response3 = mock.invoke([])
        self.assertEqual(response3.content, "Response 1")

        print("✅ Mock LLM functionality test passed")


def main():
    """Run the mock LLM tests."""
    print("🧪 Running DaBot Agent Mock LLM Tests")
    print("=" * 50)

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAgentWithMock)

    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity = 2)
    result = runner.run(suite)

    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All mock LLM tests passed!")
        print("✅ Your DaBot Agent works correctly with mock LLM.")
        print("🚀 You can now test with a real LLM server.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print(f"Failed: {len(result.failures)}, Errors: {len(result.errors)}")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
