#!/usr/bin/env python3
"""
Test Result Processor for LLM Testing Framework

Automatically processes test results after test completion:
1. Creates averaged JSON files for models with multiple test runs
2. Creates model comparison files when multiple averaged results exist

Features:
- Automatic model result averaging
- Multi-model performance comparison
- Structure validation and error logging
- Comprehensive metrics analysis
"""

import os
import json
import glob
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import statistics


class ResultProcessor:
    """Processes test results for averaging and comparison."""
    
    def __init__(self, test_logs_dir: str = None):
        """Initialize result processor."""
        if test_logs_dir is None:
            # Default to tests/test_logs directory
            tests_dir = Path(__file__).parent
            test_logs_dir = tests_dir / "test_logs"
        
        self.test_logs_dir = Path(test_logs_dir)
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logger for result processing."""
        logger = logging.getLogger("result_processor")
        logger.handlers.clear()
        logger.setLevel(logging.INFO)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter('%(levelname)s | %(message)s')
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        logger.propagate = False
        
        return logger
    
    def _extract_model_name_from_filename(self, filename: str) -> Optional[str]:
        """Extract model name from test result filename."""
        # Expected format: {model_name}_test_{timestamp}.json
        if not filename.endswith('.json'):
            return None
        
        # Remove .json extension
        base_name = filename[:-5]
        
        # Find the last occurrence of '_test_'
        test_index = base_name.rfind('_test_')
        if test_index == -1:
            return None
        
        # Extract model name (everything before '_test_')
        model_name = base_name[:test_index]
        return model_name
    
    def _get_model_test_files(self) -> Dict[str, List[Path]]:
        """Get all test files grouped by model name."""
        model_files = {}
        
        # Find all JSON files in test_logs directory
        json_files = list(self.test_logs_dir.glob("*.json"))
        
        for json_file in json_files:
            # Skip average and comparison files
            if "_average.json" in json_file.name or "model_comparison.json" in json_file.name:
                continue
            
            model_name = self._extract_model_name_from_filename(json_file.name)
            if model_name:
                if model_name not in model_files:
                    model_files[model_name] = []
                model_files[model_name].append(json_file)
        
        return model_files
    
    def _validate_json_structure(self, json_files: List[Path]) -> bool:
        """Validate that all JSON files have the same structure."""
        if len(json_files) < 2:
            return True
        
        try:
            # Load first file as reference
            with open(json_files[0], 'r') as f:
                reference_data = json.load(f)
            
            reference_keys = set(reference_data.keys())
            if "test_results" in reference_data:
                if reference_data["test_results"]:
                    reference_test_keys = set(reference_data["test_results"][0].keys())
                else:
                    reference_test_keys = set()
            else:
                reference_test_keys = set()
            
            # Compare with other files
            for json_file in json_files[1:]:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                # Check top-level keys
                if set(data.keys()) != reference_keys:
                    self.logger.error(f"Structure mismatch in {json_file.name}: different top-level keys")
                    return False
                
                # Check test result keys if they exist
                if "test_results" in data and data["test_results"]:
                    test_keys = set(data["test_results"][0].keys())
                    if test_keys != reference_test_keys:
                        self.logger.error(f"Structure mismatch in {json_file.name}: different test result keys")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating JSON structure: {e}")
            return False
    
    def _average_test_results(self, json_files: List[Path]) -> Dict[str, Any]:
        """Create averaged results from multiple test files."""
        all_data = []
        
        # Load all JSON files
        for json_file in json_files:
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                    all_data.append(data)
            except Exception as e:
                self.logger.error(f"Error loading {json_file.name}: {e}")
                continue
        
        if not all_data:
            return {}
        
        # Use first file as template
        averaged_data = all_data[0].copy()
        
        # Update metadata
        averaged_data["test_type"] = "averaged_results"
        averaged_data["source_files"] = [f.name for f in json_files]
        averaged_data["averaged_at"] = datetime.now().isoformat()
        averaged_data["num_runs"] = len(all_data)
        
        # Average the test results
        if "test_results" in averaged_data and all_data[0]["test_results"]:
            averaged_test_results = []
            
            # Get number of tests (assuming all files have same tests)
            num_tests = len(all_data[0]["test_results"])
            
            for test_idx in range(num_tests):
                # Collect all results for this test across all runs
                test_results = []
                for data in all_data:
                    if test_idx < len(data["test_results"]):
                        test_results.append(data["test_results"][test_idx])
                
                if test_results:
                    averaged_test = self._average_single_test(test_results)
                    averaged_test_results.append(averaged_test)
            
            averaged_data["test_results"] = averaged_test_results
        
        # Update summary if it exists
        if "summary" in averaged_data:
            self._update_averaged_summary(averaged_data, all_data)
        
        return averaged_data

    def _average_single_test(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Average results for a single test across multiple runs."""
        if not test_results:
            return {}

        # Use first test as template
        averaged_test = test_results[0].copy()

        # Numeric fields to average
        numeric_fields = [
            "latency_ms", "latency_per_token", "prompt_tokens", "response_tokens",
            "total_tokens", "max_tokens_requested", "max_tokens_available",
            "max_tokens_used", "score_wanted_patterns", "score_unwanted_patterns",
            "score_looping"
        ]

        # Average numeric fields
        for field in numeric_fields:
            if field in averaged_test:
                # For score fields, only average successful tests (non-null values)
                if field in ["score_unwanted_patterns", "score_looping"]:
                    values = [test.get(field) for test in test_results if test.get(field) is not None]
                    if values:
                        averaged_test[field] = round(statistics.mean(values), 2)
                    else:
                        averaged_test[field] = None  # No successful tests to average
                else:
                    # For other fields, use 0 as default
                    values = [test.get(field, 0) for test in test_results if test.get(field) is not None]
                    if values:
                        averaged_test[field] = round(statistics.mean(values), 2)

        # Boolean fields - use majority vote
        success_votes = [test.get("success", False) for test in test_results]
        averaged_test["success"] = success_votes.count(True) > len(success_votes) / 2

        # List fields - combine and deduplicate
        list_fields = ["found_patterns", "missing_patterns", "found_unwanted_patterns"]
        for field in list_fields:
            if field in averaged_test:
                all_items = []
                for test in test_results:
                    if field in test and isinstance(test[field], list):
                        all_items.extend(test[field])
                averaged_test[field] = list(set(all_items))  # Remove duplicates

        # Add metadata about averaging
        averaged_test["num_runs"] = len(test_results)
        averaged_test["success_rate"] = success_votes.count(True) / len(success_votes)

        return averaged_test

    def _update_averaged_summary(self, averaged_data: Dict[str, Any], all_data: List[Dict[str, Any]]):
        """Update summary section with averaged values."""
        summaries = [data.get("summary", {}) for data in all_data if "summary" in data]

        if not summaries:
            return

        summary = averaged_data["summary"]

        # Average numeric summary fields
        numeric_summary_fields = ["total_tests", "passed_tests", "success_rate"]
        for field in numeric_summary_fields:
            values = [s.get(field, 0) for s in summaries if s.get(field) is not None]
            if values:
                summary[field] = round(statistics.mean(values), 2)

        summary["num_runs"] = len(all_data)
        summary["averaged_at"] = datetime.now().isoformat()

    def _create_averaged_file(self, model_name: str, json_files: List[Path]) -> bool:
        """Create averaged JSON file for a model."""
        try:
            self.logger.info(f"Creating averaged results for model: {model_name}")

            # Validate structure consistency
            if not self._validate_json_structure(json_files):
                self.logger.error(f"Cannot create average for {model_name}: structure validation failed")
                return False

            # Create averaged data
            averaged_data = self._average_test_results(json_files)
            if not averaged_data:
                self.logger.error(f"Failed to create averaged data for {model_name}")
                return False

            # Save averaged file
            output_file = self.test_logs_dir / f"{model_name}_average.json"
            with open(output_file, 'w') as f:
                json.dump(averaged_data, f, indent=2)

            self.logger.info(f"✅ Created averaged file: {output_file.name}")
            return True

        except Exception as e:
            self.logger.error(f"Error creating averaged file for {model_name}: {e}")
            return False

    def _get_averaged_files(self) -> List[Path]:
        """Get all averaged JSON files."""
        return list(self.test_logs_dir.glob("*_average.json"))

    def _validate_averaged_files_structure(self, averaged_files: List[Path]) -> bool:
        """Validate that all averaged files have compatible structure."""
        return self._validate_json_structure(averaged_files)

    def process_model_averaging(self):
        """Process model averaging for all models with multiple test runs."""
        self.logger.info("🔍 Scanning for models with multiple test runs...")

        model_files = self._get_model_test_files()

        if not model_files:
            self.logger.info("No test files found.")
            return

        processed_models = 0

        for model_name, json_files in model_files.items():
            file_count = len(json_files)

            if file_count <= 1:
                self.logger.info(f"Model {model_name}: {file_count} file(s) - skipping averaging")
                continue

            self.logger.info(f"Model {model_name}: {file_count} files - processing averaging")

            if self._create_averaged_file(model_name, json_files):
                processed_models += 1

        if processed_models > 0:
            self.logger.info(f"✅ Processed averaging for {processed_models} model(s)")
        else:
            self.logger.info("No models required averaging")

    def _create_model_comparison(self, averaged_files: List[Path]) -> Dict[str, Any]:
        """Create comprehensive model comparison from averaged files."""
        comparison_data = {
            "comparison_type": "model_comparison",
            "created_at": datetime.now().isoformat(),
            "models_compared": [],
            "overall_rankings": {},
            "category_analysis": {},
            "complexity_analysis": {},
            "detailed_metrics": {},
            "summary": {}
        }

        # Load all averaged data
        models_data = {}
        for avg_file in averaged_files:
            try:
                with open(avg_file, 'r') as f:
                    data = json.load(f)
                    model_name = data.get("model_name", avg_file.stem.replace("_average", ""))
                    models_data[model_name] = data
                    comparison_data["models_compared"].append(model_name)
            except Exception as e:
                self.logger.error(f"Error loading {avg_file.name}: {e}")
                continue

        if len(models_data) < 2:
            return comparison_data

        # Calculate overall rankings
        comparison_data["overall_rankings"] = self._calculate_overall_rankings(models_data)

        # Analyze by category
        comparison_data["category_analysis"] = self._analyze_by_category(models_data)

        # Analyze by complexity level
        comparison_data["complexity_analysis"] = self._analyze_by_complexity(models_data)

        # Detailed metrics comparison
        comparison_data["detailed_metrics"] = self._create_detailed_metrics_comparison(models_data)

        # Create summary
        comparison_data["summary"] = self._create_comparison_summary(models_data)

        return comparison_data

    def _calculate_overall_rankings(self, models_data: Dict[str, Dict]) -> Dict[str, Any]:
        """Calculate overall performance rankings for models."""
        rankings = {
            "by_success_rate": [],
            "by_avg_latency_per_token": [],
            "by_score_wanted_patterns": [],
            "by_score_unwanted_patterns": [],
            "by_score_looping": [],
            "composite_score": []
        }

        model_metrics = {}

        for model_name, data in models_data.items():
            if "test_results" not in data or not data["test_results"]:
                continue

            test_results = data["test_results"]

            # Calculate aggregate metrics
            success_rate = sum(1 for t in test_results if t.get("success", False)) / len(test_results)
            avg_latency_per_token = statistics.mean([t.get("latency_per_token", 0) for t in test_results])
            avg_score_wanted = statistics.mean([t.get("score_wanted_patterns", 0) for t in test_results])

            # For unwanted/looping scores, only average non-null values (successful tests)
            unwanted_scores = [t.get("score_unwanted_patterns") for t in test_results if t.get("score_unwanted_patterns") is not None]
            looping_scores = [t.get("score_looping") for t in test_results if t.get("score_looping") is not None]

            avg_score_unwanted = statistics.mean(unwanted_scores) if unwanted_scores else 0
            avg_score_looping = statistics.mean(looping_scores) if looping_scores else 0

            # Composite score (higher is better, so invert latency)
            composite_score = (
                success_rate * 0.3 +
                avg_score_wanted * 0.25 +
                avg_score_unwanted * 0.25 +
                avg_score_looping * 0.2 -
                (avg_latency_per_token / 1000) * 0.1  # Penalty for high latency
            )

            model_metrics[model_name] = {
                "success_rate": round(success_rate, 2),
                "avg_latency_per_token": round(avg_latency_per_token, 2),
                "avg_score_wanted_patterns": round(avg_score_wanted, 2),
                "avg_score_unwanted_patterns": round(avg_score_unwanted, 2),
                "avg_score_looping": round(avg_score_looping, 2),
                "composite_score": round(composite_score, 2)
            }

        # Create rankings
        rankings["by_success_rate"] = sorted(model_metrics.items(),
                                           key=lambda x: x[1]["success_rate"], reverse=True)
        rankings["by_avg_latency_per_token"] = sorted(model_metrics.items(),
                                                    key=lambda x: x[1]["avg_latency_per_token"])
        rankings["by_score_wanted_patterns"] = sorted(model_metrics.items(),
                                                    key=lambda x: x[1]["avg_score_wanted_patterns"], reverse=True)
        rankings["by_score_unwanted_patterns"] = sorted(model_metrics.items(),
                                                      key=lambda x: x[1]["avg_score_unwanted_patterns"], reverse=True)
        rankings["by_score_looping"] = sorted(model_metrics.items(),
                                            key=lambda x: x[1]["avg_score_looping"], reverse=True)
        rankings["composite_score"] = sorted(model_metrics.items(),
                                           key=lambda x: x[1]["composite_score"], reverse=True)

        return rankings

    def _analyze_by_category(self, models_data: Dict[str, Dict]) -> Dict[str, Any]:
        """Analyze model performance by test category."""
        category_analysis = {}

        # Collect all categories
        all_categories = set()
        for data in models_data.values():
            if "test_results" in data:
                for test in data["test_results"]:
                    if "category" in test:
                        all_categories.add(test["category"])

        # Analyze each category
        for category in all_categories:
            category_analysis[category] = {}

            for model_name, data in models_data.items():
                if "test_results" not in data:
                    continue

                category_tests = [t for t in data["test_results"] if t.get("category") == category]

                if category_tests:
                    success_rate = sum(1 for t in category_tests if t.get("success", False)) / len(category_tests)
                    avg_latency = statistics.mean([t.get("latency_per_token", 0) for t in category_tests])
                    avg_score_wanted = statistics.mean([t.get("score_wanted_patterns", 0) for t in category_tests])

                    category_analysis[category][model_name] = {
                        "success_rate": round(success_rate, 2),
                        "avg_latency_per_token": round(avg_latency, 2),
                        "avg_score_wanted_patterns": round(avg_score_wanted, 2),
                        "test_count": len(category_tests)
                    }

        return category_analysis

    def _analyze_by_complexity(self, models_data: Dict[str, Dict]) -> Dict[str, Any]:
        """Analyze model performance by complexity level."""
        complexity_analysis = {}

        # Collect all complexity levels
        all_levels = set()
        for data in models_data.values():
            if "test_results" in data:
                for test in data["test_results"]:
                    if "complexity_level" in test:
                        all_levels.add(test["complexity_level"])

        # Analyze each complexity level
        for level in sorted(all_levels):
            complexity_analysis[f"level_{level}"] = {}

            for model_name, data in models_data.items():
                if "test_results" not in data:
                    continue

                level_tests = [t for t in data["test_results"] if t.get("complexity_level") == level]

                if level_tests:
                    success_rate = sum(1 for t in level_tests if t.get("success", False)) / len(level_tests)
                    avg_latency = statistics.mean([t.get("latency_per_token", 0) for t in level_tests])
                    avg_score_wanted = statistics.mean([t.get("score_wanted_patterns", 0) for t in level_tests])

                    complexity_analysis[f"level_{level}"][model_name] = {
                        "success_rate": round(success_rate, 2),
                        "avg_latency_per_token": round(avg_latency, 2),
                        "avg_score_wanted_patterns": round(avg_score_wanted, 2),
                        "test_count": len(level_tests)
                    }

        return complexity_analysis

    def _create_detailed_metrics_comparison(self, models_data: Dict[str, Dict]) -> Dict[str, Any]:
        """Create detailed side-by-side metrics comparison."""
        detailed_metrics = {
            "models": {},
            "metric_ranges": {}
        }

        all_metrics = []

        for model_name, data in models_data.items():
            if "test_results" not in data or not data["test_results"]:
                continue

            test_results = data["test_results"]

            model_metrics = {
                "total_tests": len(test_results),
                "passed_tests": sum(1 for t in test_results if t.get("success", False)),
                "success_rate": sum(1 for t in test_results if t.get("success", False)) / len(test_results),
                "avg_latency_ms": statistics.mean([t.get("latency_ms", 0) for t in test_results]),
                "avg_latency_per_token": statistics.mean([t.get("latency_per_token", 0) for t in test_results]),
                "avg_prompt_tokens": statistics.mean([t.get("prompt_tokens", 0) for t in test_results]),
                "avg_response_tokens": statistics.mean([t.get("response_tokens", 0) for t in test_results]),
                "avg_total_tokens": statistics.mean([t.get("total_tokens", 0) for t in test_results]),
                "avg_score_wanted_patterns": statistics.mean([t.get("score_wanted_patterns", 0) for t in test_results]),
                "avg_score_unwanted_patterns": statistics.mean([t.get("score_unwanted_patterns") for t in test_results if t.get("score_unwanted_patterns") is not None] or [0]),
                "avg_score_looping": statistics.mean([t.get("score_looping") for t in test_results if t.get("score_looping") is not None] or [0]),
                "min_latency_per_token": min([t.get("latency_per_token", 0) for t in test_results]),
                "max_latency_per_token": max([t.get("latency_per_token", 0) for t in test_results])
            }

            # Round values
            for key, value in model_metrics.items():
                if isinstance(value, float):
                    model_metrics[key] = round(value, 2)

            detailed_metrics["models"][model_name] = model_metrics
            all_metrics.append(model_metrics)

        # Calculate metric ranges for context
        if all_metrics:
            detailed_metrics["metric_ranges"] = {
                "success_rate": {
                    "min": round(min(m["success_rate"] for m in all_metrics), 2),
                    "max": round(max(m["success_rate"] for m in all_metrics), 2),
                    "avg": round(statistics.mean([m["success_rate"] for m in all_metrics]), 2)
                },
                "avg_latency_per_token": {
                    "min": round(min(m["avg_latency_per_token"] for m in all_metrics), 2),
                    "max": round(max(m["avg_latency_per_token"] for m in all_metrics), 2),
                    "avg": round(statistics.mean([m["avg_latency_per_token"] for m in all_metrics]), 2)
                }
            }

        return detailed_metrics

    def _create_comparison_summary(self, models_data: Dict[str, Dict]) -> Dict[str, Any]:
        """Create high-level comparison summary."""
        summary = {
            "total_models": len(models_data),
            "best_overall_model": None,
            "fastest_model": None,
            "most_accurate_model": None,
            "recommendations": []
        }

        if len(models_data) < 2:
            return summary

        # Find best models in different categories
        model_scores = {}

        for model_name, data in models_data.items():
            if "test_results" not in data or not data["test_results"]:
                continue

            test_results = data["test_results"]
            success_rate = sum(1 for t in test_results if t.get("success", False)) / len(test_results)
            avg_latency = statistics.mean([t.get("latency_per_token", 0) for t in test_results])
            avg_score_wanted = statistics.mean([t.get("score_wanted_patterns", 0) for t in test_results])

            model_scores[model_name] = {
                "success_rate": success_rate,
                "avg_latency_per_token": avg_latency,
                "avg_score_wanted_patterns": avg_score_wanted
            }

        # Determine best models
        if model_scores:
            summary["most_accurate_model"] = max(model_scores.items(), key=lambda x: x[1]["success_rate"])[0]
            summary["fastest_model"] = min(model_scores.items(), key=lambda x: x[1]["avg_latency_per_token"])[0]

            # Best overall (composite score)
            composite_scores = {}
            for model, scores in model_scores.items():
                composite = scores["success_rate"] * 0.6 + scores["avg_score_wanted_patterns"] * 0.4 - (scores["avg_latency_per_token"] / 1000) * 0.1
                composite_scores[model] = composite

            summary["best_overall_model"] = max(composite_scores.items(), key=lambda x: x[1])[0]

        return summary

    def process_model_comparison(self):
        """Process model comparison if multiple averaged files exist."""
        self.logger.info("🔍 Scanning for averaged model files...")

        averaged_files = self._get_averaged_files()

        if len(averaged_files) <= 1:
            self.logger.info(f"Found {len(averaged_files)} averaged file(s) - skipping comparison")
            return

        self.logger.info(f"Found {len(averaged_files)} averaged files - processing comparison")

        # Validate structure consistency
        if not self._validate_averaged_files_structure(averaged_files):
            self.logger.error("Cannot create comparison: structure validation failed")
            return

        try:
            # Create comparison data
            comparison_data = self._create_model_comparison(averaged_files)

            if not comparison_data.get("models_compared"):
                self.logger.error("No valid model data found for comparison")
                return

            # Save comparison file
            output_file = self.test_logs_dir / "model_comparison.json"
            with open(output_file, 'w') as f:
                json.dump(comparison_data, f, indent=2)

            self.logger.info(f"✅ Created model comparison: {output_file.name}")

            # Create markdown comparison file
            self._create_markdown_comparison(comparison_data)

            # Print summary
            self._print_comparison_summary(comparison_data)

        except Exception as e:
            self.logger.error(f"Error creating model comparison: {e}")

    def _create_markdown_comparison(self, comparison_data: Dict[str, Any]):
        """Create human-readable markdown comparison file."""
        try:
            markdown_content = self._generate_markdown_content(comparison_data)

            # Save markdown file
            output_file = self.test_logs_dir / "model_comparison.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            self.logger.info(f"✅ Created markdown comparison: {output_file.name}")

        except Exception as e:
            self.logger.error(f"Error creating markdown comparison: {e}")

    def _generate_markdown_content(self, comparison_data: Dict[str, Any]) -> str:
        """Generate comprehensive markdown content for model comparison."""
        models = comparison_data.get("models_compared", [])
        created_at = comparison_data.get("created_at", "")

        # Start building markdown content
        md_lines = []

        # Header
        md_lines.extend([
            "# 🏆 LLM Model Comparison Report",
            "",
            f"**Generated:** {created_at}",
            f"**Models Compared:** {len(models)}",
            "",
            "---",
            ""
        ])

        # Executive Summary
        summary = comparison_data.get("summary", {})
        if summary:
            md_lines.extend([
                "## 📊 Executive Summary",
                "",
                f"| Metric | Winner |",
                f"|--------|--------|",
                f"| 🏆 **Best Overall** | `{summary.get('best_overall_model', 'N/A')}` |",
                f"| 🎯 **Most Accurate** | `{summary.get('most_accurate_model', 'N/A')}` |",
                f"| ⚡ **Fastest** | `{summary.get('fastest_model', 'N/A')}` |",
                "",
                "---",
                ""
            ])

        # Overall Rankings
        rankings = comparison_data.get("overall_rankings", {})
        if rankings:
            md_lines.extend([
                "## 🏅 Overall Performance Rankings",
                ""
            ])

            # Success Rate Ranking
            if "by_success_rate" in rankings:
                md_lines.extend([
                    "### 🎯 Success Rate Ranking",
                    "",
                    "| Rank | Model | Success Rate | Avg Latency/Token | Composite Score |",
                    "|------|-------|--------------|-------------------|-----------------|"
                ])

                for i, (model, metrics) in enumerate(rankings["by_success_rate"], 1):
                    success_rate = f"{metrics['success_rate']:.2%}"
                    latency = f"{metrics['avg_latency_per_token']:.2f}ms"
                    composite = f"{metrics['composite_score']:.2f}"
                    md_lines.append(f"| {i} | `{model}` | {success_rate} | {latency} | {composite} |")

                md_lines.extend(["", ""])

            # Latency Ranking
            if "by_avg_latency_per_token" in rankings:
                md_lines.extend([
                    "### ⚡ Speed Ranking (Lower is Better)",
                    "",
                    "| Rank | Model | Avg Latency/Token | Success Rate |",
                    "|------|-------|-------------------|--------------|"
                ])

                for i, (model, metrics) in enumerate(rankings["by_avg_latency_per_token"], 1):
                    latency = f"{metrics['avg_latency_per_token']:.2f}ms"
                    success_rate = f"{metrics['success_rate']:.2%}"
                    md_lines.append(f"| {i} | `{model}` | {latency} | {success_rate} |")

                md_lines.extend(["", ""])

        # Detailed Metrics Comparison
        detailed_metrics = comparison_data.get("detailed_metrics", {})
        if detailed_metrics and "models" in detailed_metrics:
            md_lines.extend([
                "---",
                "",
                "## 📈 Detailed Metrics Comparison",
                "",
                "| Metric | " + " | ".join([f"`{model}`" for model in models]) + " |",
                "|" + "|".join(["--------"] * (len(models) + 1)) + "|"
            ])

            # Key metrics to display
            metric_labels = {
                "success_rate": "Success Rate",
                "avg_latency_per_token": "Avg Latency/Token (ms)",
                "avg_prompt_tokens": "Avg Prompt Tokens",
                "avg_response_tokens": "Avg Response Tokens",
                "avg_score_wanted_patterns": "Pattern Match Score",
                "avg_score_unwanted_patterns": "Clean Response Score",
                "avg_score_looping": "Non-Looping Score"
            }

            for metric_key, metric_label in metric_labels.items():
                row = [metric_label]
                for model in models:
                    model_data = detailed_metrics["models"].get(model, {})
                    value = model_data.get(metric_key, 0)

                    if metric_key == "success_rate":
                        formatted_value = f"{value:.2%}"
                    elif "score" in metric_key:
                        formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = f"{value:.2f}"

                    row.append(formatted_value)

                md_lines.append("| " + " | ".join(row) + " |")

            md_lines.extend(["", ""])

        # Category Analysis
        category_analysis = comparison_data.get("category_analysis", {})
        if category_analysis:
            md_lines.extend([
                "---",
                "",
                "## 📚 Performance by Category",
                ""
            ])

            for category, category_data in category_analysis.items():
                md_lines.extend([
                    f"### {category}",
                    "",
                    "| Model | Success Rate | Avg Latency/Token | Pattern Score | Tests |",
                    "|-------|--------------|-------------------|---------------|-------|"
                ])

                # Sort models by success rate for this category
                sorted_models = sorted(category_data.items(),
                                     key=lambda x: x[1].get("success_rate", 0), reverse=True)

                for model, metrics in sorted_models:
                    success_rate = f"{metrics.get('success_rate', 0):.2%}"
                    latency = f"{metrics.get('avg_latency_per_token', 0):.2f}ms"
                    pattern_score = f"{metrics.get('avg_score_wanted_patterns', 0):.2f}"
                    test_count = metrics.get('test_count', 0)

                    md_lines.append(f"| `{model}` | {success_rate} | {latency} | {pattern_score} | {test_count} |")

                md_lines.extend(["", ""])

        # Complexity Analysis
        complexity_analysis = comparison_data.get("complexity_analysis", {})
        if complexity_analysis:
            md_lines.extend([
                "---",
                "",
                "## 🎚️ Performance by Complexity Level",
                ""
            ])

            # Sort complexity levels
            sorted_levels = sorted(complexity_analysis.keys(),
                                 key=lambda x: int(x.split('_')[1]) if '_' in x else 0)

            for level_key in sorted_levels:
                level_data = complexity_analysis[level_key]
                level_num = level_key.replace('level_', '')

                md_lines.extend([
                    f"### Level {level_num}",
                    "",
                    "| Model | Success Rate | Avg Latency/Token | Pattern Score | Tests |",
                    "|-------|--------------|-------------------|---------------|-------|"
                ])

                # Sort models by success rate for this level
                sorted_models = sorted(level_data.items(),
                                     key=lambda x: x[1].get("success_rate", 0), reverse=True)

                for model, metrics in sorted_models:
                    success_rate = f"{metrics.get('success_rate', 0):.2%}"
                    latency = f"{metrics.get('avg_latency_per_token', 0):.2f}ms"
                    pattern_score = f"{metrics.get('avg_score_wanted_patterns', 0):.2f}"
                    test_count = metrics.get('test_count', 0)

                    md_lines.append(f"| `{model}` | {success_rate} | {latency} | {pattern_score} | {test_count} |")

                md_lines.extend(["", ""])

        # Footer
        md_lines.extend([
            "---",
            "",
            "## 📝 Notes",
            "",
            "- **Success Rate**: Percentage of tests passed",
            "- **Avg Latency/Token**: Average response time per token generated",
            "- **Pattern Match Score**: How well the model matches expected patterns (0-1)",
            "- **Clean Response Score**: How well the model avoids unwanted patterns (0-1)",
            "- **Non-Looping Score**: How well the model avoids repetitive responses (0-1)",
            "- **Composite Score**: Weighted overall performance score",
            "",
            f"*Report generated on {created_at}*"
        ])

        return "\n".join(md_lines)

    def _print_comparison_summary(self, comparison_data: Dict[str, Any]):
        """Print a brief summary of the model comparison."""
        summary = comparison_data.get("summary", {})
        models = comparison_data.get("models_compared", [])

        print(f"\n📊 MODEL COMPARISON SUMMARY")
        print("=" * 50)
        print(f"Models Compared: {', '.join(models)}")

        if summary.get("best_overall_model"):
            print(f"🏆 Best Overall: {summary['best_overall_model']}")
        if summary.get("most_accurate_model"):
            print(f"🎯 Most Accurate: {summary['most_accurate_model']}")
        if summary.get("fastest_model"):
            print(f"⚡ Fastest: {summary['fastest_model']}")

        print("=" * 50)


def process_test_results():
    """Main function to process test results for averaging and comparison."""
    processor = ResultProcessor()

    print("🔄 Processing test results...")

    # Step 1: Process model averaging
    processor.process_model_averaging()

    # Step 2: Process model comparison
    processor.process_model_comparison()

    print("✅ Test result processing completed!")


if __name__ == "__main__":
    process_test_results()
