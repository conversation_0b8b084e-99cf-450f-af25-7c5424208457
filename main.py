#!/usr/bin/env python3
"""
Main entry point for the DaBot Agent - LangGraph Edition.

This script provides the main entry point for running the LangGraph-based
AI agent with various modes and configurations.
"""

import os
import sys
import time
import uuid

# Add current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.graphs.orchestrator import AgentOrchestrator
from core.graphs.visualize_graph import log_graph_structure
from utils.helpers import sanitize_input
from utils.model_info import get_model_name_from_server
from config import LLAMACPP_API_URL, APP_NAME


def main():
    """Main entry point for the CLI application - goes straight to interactive mode."""
    print(f"🤖 {APP_NAME}")
    print("=" * 50)

    try:
        print(f"\n🔗 LLM Server: {LLAMACPP_API_URL}")
        print(f"\n🔗 LLM Model: {get_model_name_from_server()}")

        # Initialize orchestrator and log graph structure to file
        orchestrator = AgentOrchestrator()
        print("\n✅ Agent initialized successfully")

        # Log graph structure to file instead of terminal
        import logging
        graph_logger = logging.getLogger("graph_structure")
        graph_logger.setLevel(logging.INFO)

        # Create file handler if not exists
        if not graph_logger.handlers:
            import os
            log_file = os.path.join(os.path.dirname(__file__), "dabot-agent.log")
            file_handler = logging.FileHandler(log_file)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            graph_logger.addHandler(file_handler)

        # Use dynamic graph structure visualization
        log_graph_structure(orchestrator.graph, graph_logger)

        # Go straight to interactive mode
        print("\n🗣️ Starting Interactive Mode...")
        interactive_mode(orchestrator)

    except KeyboardInterrupt:
        print("\n\nGoodbye! 👋")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def interactive_mode(orchestrator):
    """Run the agent in interactive chat mode."""
    print("\n🗣️  Interactive Mode")
    print("=" * 30)
    print("Type 'exit' or 'quit' to end the conversation")
    print("Type 'help' for available commands")
    print()

    # Generate conversation thread
    thread_id = str(uuid.uuid4())

    try:
        while True:
            # Get user input
            user_input = input("💬 You: ").strip()

            if not user_input:
                continue

            # Handle special commands
            if user_input.lower() in ['exit', 'quit']:
                print("Goodbye! 👋")
                break
            elif user_input.lower() == 'help':
                show_help()
                continue
            elif user_input.lower() == 'clear':
                # Start new conversation
                thread_id = str(uuid.uuid4())
                print("🔄 Started new conversation")
                continue

            # Sanitize input
            user_input = sanitize_input(user_input)

            print(f"\n🤖 Agent Processing: {user_input}")
            print("=" * 60)

            try:
                # Show processing status
                print("🔄 Analyzing request...", flush = True)
                time.sleep(0.1)  # Small delay to ensure proper output ordering

                # Run conversation
                result = orchestrator.run_conversation(user_input, thread_id)

                print("✅ Processing complete!", flush = True)

                # Display final response
                if result["success"]:
                    print(f"\n🤖 Agent: {result['final_answer']}")
                else:
                    print(f"\n❌ Error: {result['error']}")

            except Exception as e:
                print(f"\n❌ Error: {e}")
                print("💡 Check your LLM server connection and configuration")

            print("\n" + "-" * 60)

    except KeyboardInterrupt:
        print("\n\nConversation ended. 👋")


def show_help():
    """Display help information."""
    print("\n📚 Available Commands:")
    print("=" * 25)
    print("• exit, quit - End the conversation")
    print("• help - Show this help message")
    print("• clear - Start a new conversation")
    print("\n💡 Example Questions:")
    print("• Hello, how are you?")
    print("• What is 2 + 2?")
    print("• Tell me about Python")
    print("• Explain machine learning")


if __name__ == "__main__":
    main()
