# 🤖 DaBot Agent - LangGraph Edition

A simplified conversational AI agent built with LangGraph, designed to work with local LLaMA.cpp models for CPU-only environments.

## 🎯 Key Features

- **🏗️ Simplified LangGraph Flow**: Clean START → LLM → END architecture
- **🚀 Local LLM Support**: Optimized for LLaMA.cpp server integration
- **📝 Proper Message Patterns**: Follows LangGraph best practices with System/Human/AI messages
- **⚙️ Centralized Configuration**: All constants managed in single config.py module
- **🖥️ Interactive CLI**: Multiple interaction modes (interactive, demo, test)
- **🚀 CPU Optimized**: Designed for CPU-only inference with efficient prompting
- **📊 Comprehensive Logging**: Function-level logging with @log decorator

## 🏗️ Architecture

The agent uses an enhanced flow implemented through LangGraph:

```
START → summarization_node → llm_node → END
```

**Enhanced Features:**

- **Intelligent Summarization**: Automatically summarizes long conversations with quality prompts
- **Context Management**: Maintains conversation continuity while respecting token limits
- **Dynamic Token Calculation**: All max_tokens calculated by prompt_manager.py

### Current Flow Diagram

```mermaid
graph TD
    A[User Input] --> B[main.py]
    B --> C[AgentOrchestrator.run_conversation]
    C --> D[create_initial_state]
    D --> E[AgentState]
    E --> F[LangGraph.stream]
    F --> G[summarization_node]
    G --> H[Check if summarization needed]
    H --> I[Create/update summary if needed]
    I --> J[llm_node]

    J --> K[Extract user_input from state]
    K --> L[Create LlamaCppProvider]
    L --> M[Get LLM instance]
    M --> N[Create Messages Array with Summary Context]
    N --> O[SystemMessage: 'You are a helpful AI assistant...' + Summary]
    O --> P[HumanMessage: user_input]
    P --> Q[Calculate optimal max_tokens via prompt_manager]
    Q --> R[LLM.invoke with messages and optimal max_tokens]
    R --> S[LLM Response]
    S --> T[Update state with final_answer]
    T --> U[Add AIMessage to messages]
    U --> V[Return updated state]

    V --> W[Extract conversation result]
    W --> X[Return to user]
```

### Enhanced Summarization System

The reworked summarization system provides intelligent conversation management:

**Key Features:**

- **SummaryManager Class**: Advanced summarization logic with multiple triggering factors
- **Quality Prompts**: Enhanced prompts focusing on key facts, user preferences, and context
- **Incremental Updates**: Merges new information with existing summaries for continuity
- **Error Handling**: Robust fallback mechanisms for LLM failures
- **Performance Optimization**: Avoids over-summarization with intelligent thresholds
- **Token Management**: Optimal max_tokens calculation for summary generation

**Summarization Triggers:**

- Message count exceeds window_size (default: 5)
- Avoids over-summarization when recent summary exists
- Considers conversation context and summary quality

### Message Flow

The agent follows LangGraph best practices with proper message roles:

1. **SystemMessage**: Sets the AI assistant context and behavior
2. **HumanMessage**: Contains the user's input/question
3. **AIMessage**: Contains the LLM's response

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- LLaMA.cpp server running (default: `http://************:11111/completion`)
- Virtual environment recommended

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd dabot-agent
```

2. Create and activate virtual environment:
```bash
python -m venv .venv-dabot-agent
source .venv-dabot-agent/bin/activate  # Linux/Mac
# or
.venv-dabot-agent\Scripts\activate  # Windows
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables (optional):
```bash
export LLAMACPP_API_URL="http://your-server:port/completion"
export LOG_LEVEL="DEBUG"
```

### Running the Agent

```bash
python main.py
```

Choose from the available modes:
1. **Interactive Mode**: Chat with the agent
2. **Demo Mode**: See predefined scenarios
3. **Test Connection**: Verify LLM server connectivity

## ⚙️ Configuration

All configuration is centralized in `config.py`. Key constants include:

### LLM Configuration
- `LLAMACPP_API_URL`: LLaMA.cpp server endpoint
- `LLAMACPP_MAX_TOKENS`: Maximum tokens for responses (250)
- `MODEL_PROVIDER`: Model provider type ("llamacpp")
- `MODEL_NAME`: Model name (automatically fetched from server via /props endpoint)

### Agent Configuration

- `MAX_ITERATIONS`: Maximum reasoning steps (1)
- `VERBOSE`: Enable verbose output

### Logging Configuration
- `LOG_LEVEL`: Logging level ("DEBUG")
- `LOG_FILE`: Log file path ("dabot-agent.log")

### Prompt Manager Configuration

- `MAX_CONTEXT_TOKENS`: Maximum context window (automatically fetched from server at runtime)
- `RESERVED_TOKENS`: Reserved tokens for response (50)

### Dynamic Model Configuration

The system automatically fetches model parameters from the remote llama.cpp server at runtime:

**Fully Dynamic Configuration:**

- `MAX_CONTEXT_TOKENS`: Automatically fetched from server's `n_ctx` value at runtime
- `MODEL_NAME`: Automatically fetched from server's `model_path` value at runtime
- Falls back to default values if server is unavailable
- No hardcoded constants - all values are dynamic

**Manual Server Information:**
```python
from utils.model_info import get_server_info

# Get live server information
server_info = get_server_info()
print(f"Model: {server_info['model_name']}")
print(f"Context Length: {server_info['context_length']}")
```

**Available endpoints:**

- `GET /props`: Returns model properties including model_path and n_ctx
- `GET /health`: Returns server status

This allows the system to automatically adapt to different models and configurations without manual updates.

### Enhanced Features

**Fresh LLM Instances**: The system now creates a fresh LLM instance for each main.py run, preventing cached responses
and ensuring accurate performance measurements.

**Advanced Stop Sequence Handling**: Implemented comprehensive stop sequence detection with default sequences (
`<|im_end|>`, `<|end_of_text|>`, `</s>`, `<|eot_id|>`) and logging when stop sequences are detected.

**Enhanced Logging**: Added detailed logging for:

- LLM raw responses (status, headers, body) at DEBUG level
- Max tokens calculation showing both requested and adjusted values
- Stop sequence detection and proper response termination

**Fully Dynamic Configuration**: Removed all hardcoded model constants:

- Model name automatically fetched from server at runtime
- Context length automatically fetched from server at runtime
- No static configuration dependencies - adapts to any model automatically

## 📁 Project Structure

```
dabot-agent/
├── config.py              # Centralized configuration constants
├── main.py                # CLI entry point
├── core/                  # Core LangGraph components
│   ├── graphs/           # Graph definitions and orchestration
│   ├── nodes/            # LLM reasoning node
│   └── state.py          # Simplified state management
├── models/               # LLM provider implementations
├── tools/                # Tool registry (preserved for future use)
├── utils/                # Utility functions
│   └── model_info.py     # Dynamic model parameter fetching
└── logger/               # Logging utilities with @log decorator
```

## 📊 State Management

The simplified `AgentState` contains:

- `messages`: List of conversation messages (System/Human/AI)
- `user_input`: Current user input string
- `final_answer`: LLM response
- `error`: Optional error information

## 🤖 LLM Integration

The system uses proper LangGraph message patterns:

```python
messages = [
    SystemMessage(content="You are a helpful AI assistant..."),
    HumanMessage(content=user_input)
]
response = llm.invoke(messages)
```

## 🔧 Tools (Preserved for Future Use)

The tool system is preserved but not used in the current simple flow:

- **get_current_date**: Get today's date
- **get_current_time**: Get current time
- **get_current_datetime**: Get both date and time
- **math_calculator**: Perform mathematical calculations

## 🛠️ Development

### Adding New Functionality

1. **Extend the Graph**: Modify `core/graphs/agent_graph.py`
2. **Add New Nodes**: Create nodes in `core/nodes/`
3. **Update State**: Modify `core/state.py` as needed
4. **Configuration**: Add constants to `config.py`

### Logging

All functions use the `@log` decorator for comprehensive logging:

```python
from logger.get_logger import log

@log
def my_function():
    # Function implementation
    pass
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Error**: Ensure LLaMA.cpp server is running and accessible
2. **Import Errors**: Verify all dependencies are installed in virtual environment
3. **Configuration Issues**: Check `config.py` constants and environment variables

### Debugging

The system provides comprehensive logging at DEBUG level:

```bash
export LOG_LEVEL="DEBUG"
python main.py
```

## 📄 License

[Add your license information here]

## 🤝 Contributing

[Add contribution guidelines here]

| Variable              | Default                                | Description                                                       |
|-----------------------|----------------------------------------|-------------------------------------------------------------------|
| `LLAMACPP_API_URL`    | `http://************:11111/completion` | LLaMA.cpp server URL                                              |
| `MAX_ITERATIONS`      | `1`                                    | Maximum reasoning steps                                           |
| `LLAMACPP_MAX_TOKENS` | `250`                                  | Max tokens per response                                           |
| `MAX_CONTEXT_TOKENS`  | `Dynamic (fetched from server)`        | Max context window (automatically fetched from server at runtime) |
| `VERBOSE`             | `true`                                 | Show detailed reasoning                                           |
| `LOG_LEVEL`           | `DEBUG`                                | Logging level                                                     |

### LLaMA.cpp Server Setup

Your server should be running with these recommended parameters:
```bash
# Example server command
./server -m your-model.gguf -c 4096 -t 8 --host 0.0.0.0 --port 11111
```

## 🧪 Testing

Test the setup without LLM server:
```bash
python test_langgraph_setup.py      # Basic setup test
python test_agent_functionality.py  # Core functionality
python test_agent_with_mock.py      # Mock LLM test
```

## 💡 Example Usage

### Programmatic Usage
```python
from core.graphs.orchestrator import AgentOrchestrator

# Initialize agent
orchestrator = AgentOrchestrator(max_steps=5)

# Run conversation
result = orchestrator.run_conversation("What's 2 + 2?")
print(result["final_answer"])  # "2 + 2 equals 4"

# Streaming conversation
for step in orchestrator.stream_conversation("Calculate sqrt(16)"):
    print(f"Step: {step['step_type']}")
    if step.get('reasoning'):
        print(f"Thinking: {step['reasoning'][-1]}")
```

## 🔄 Decision Flow

```mermaid
graph TD
    A[User Input] --> B[LLM Reasoning]
    B --> C{Tool Needed?}
    C -->|Yes| D[Select Tool]
    C -->|No| E[Direct Answer]
    D --> F[Execute Tool]
    F --> G[Process Result]
    G --> H[Final Answer]
    E --> H
    H --> I[Return to User]
```

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're in the project directory and using the correct Python environment
2. **Connection Failed**: Verify your LLaMA.cpp server is running and accessible
3. **Tool Errors**: Check that all dependencies are installed correctly

### Debug Mode
```bash
export LOG_LEVEL="DEBUG"
export VERBOSE="true"
python main.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🎉 Ready to Use!

Your agent is now ready for production use with intelligent tool decision-making capabilities. Start your LLaMA.cpp server and run `python main.py` to begin!
