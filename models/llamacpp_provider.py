"""
LLaMA.cpp model provider implementation with dynamic prompt length tracking.
"""
import json
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator

import requests
from langchain.chat_models.base import BaseChatModel
from langchain.schema.messages import (
    BaseMessage, AIMessage, AIMessageChunk
)
from langchain.schema.output import ChatGeneration, ChatGenerationChunk, ChatResult
from langchain.schema.runnable import RunnableConfig
from langchain.tools import BaseTool

from config import LLAMACPP_MAX_TOKENS, RESERVED_TOKENS, LLAMACPP_STREAM
from logger.get_logger import log, get_logger
from utils.model_info import get_context_length_from_server, fetch_model_props
from .formatters.base import ChatFormatter, GenericFormatter
from .formatters.qwen_chatml import QwenChatMLFormatter
from .prompt_manager import PromptManager


class LlamaCppChatModel(BaseChatModel):
    """LLaMA.cpp Chat Model implementation."""

    api_url: str
    max_tokens: int = LLAMACPP_MAX_TOKENS
    tools: List[BaseTool] = []
    tool_choice: Optional[Union[str, Dict]] = None

    # Default stop sequences for chat models
    default_stop_sequences: List[str] = ["<|im_end|>"]

    # Initialize PromptManager with max context tokens and reserved tokens
    prompt_manager: PromptManager = PromptManager(max_context_tokens = get_context_length_from_server(),
                                                  reserved_tokens = RESERVED_TOKENS)

    # Formatter strategy (selected per server props)
    _formatter: ChatFormatter = None

    @property
    def _llm_type(self) -> str:
        return "llama.cpp.chat"

    @log
    def bind_tools(self, tools: List[BaseTool], tool_choice: Optional[Union[str, Dict]] = None,
                   **kwargs: Any) -> BaseChatModel:
        """Bind tools to the chat model.

        Args:
            tools: The tools to bind to the chat model
            tool_choice: The tool choice to use ("auto", "any", or specific tool name)

        Returns:
            The chat model with tools bound
        """
        new_instance = self.copy()
        new_instance.tools = tools
        new_instance.tool_choice = tool_choice
        return new_instance

    def with_config(self, config: RunnableConfig) -> BaseChatModel:
        """Return a new chat model with the specified config."""
        new_instance = self.copy()
        if "tools" in config:
            new_instance.tools = config["tools"]
        return new_instance

    def copy(self) -> BaseChatModel:
        """Create a copy of the chat model."""
        new_inst = LlamaCppChatModel(
                api_url = self.api_url,
                max_tokens = self.max_tokens,
                tools = self.tools.copy() if self.tools else [],
                tool_choice = self.tool_choice,
        )
        # Preserve formatter if already selected
        new_inst._formatter = self._formatter
        return new_inst

    def _select_formatter(self) -> ChatFormatter:
        """Select formatter based on /props chat_template or model_path; fallback to Generic."""
        if self._formatter is not None:
            return self._formatter

        logger = get_logger()
        props = fetch_model_props(self.api_url)
        chat_template = None
        model_path = None
        try:
            if props:
                chat_template = props.get("default_generation_settings", {}).get("chat_format") or props.get(
                    "chat_template")
                model_path = props.get("model_path")
        except Exception:
            pass

        # Heuristics: prefer QwenChatML for Qwen models or if chat_template suggests ChatML/content-only
        use_qwen = False
        if chat_template:
            # llama.cpp Qwen often shows chat_format as "Content-only" but provides a chat_template string
            # We cannot parse that here; use model_path heuristics too
            if isinstance(chat_template, str) and (
                    "content" in chat_template.lower() or "chatml" in chat_template.lower()):
                use_qwen = True
        if model_path and isinstance(model_path, str) and "qwen" in model_path.lower():
            use_qwen = True

        self._formatter = QwenChatMLFormatter() if use_qwen else GenericFormatter()
        logger.debug(
            f"Selected chat formatter: {'QwenChatML' if use_qwen else 'Generic'} (chat_template={chat_template}, model_path={model_path})")
        return self._formatter

    def _generate(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager = None,
            **kwargs: Any,
    ) -> ChatResult:
        """Call the LLaMA.cpp API and generate a chat result."""

        headers = {"Content-Type": "application/json"}

        # Convert messages via selected formatter
        formatter = self._select_formatter()
        prompt = formatter.format(messages, tools = self.tools)

        # Requested max tokens from kwargs or default
        requested_max_tokens = kwargs.get("max_tokens", self.max_tokens)

        # Dynamically adjust max_tokens based on prompt length
        adjusted_max_tokens = self.prompt_manager.adjust_max_tokens(prompt, requested_max_tokens)

        # Raise error if prompt too long to generate any tokens
        if adjusted_max_tokens <= 0:
            raise ValueError(
                    "Prompt is too long for the context window. "
                    "Please shorten the conversation history or reduce max_tokens."
            )

        # Only send minimal parameters - let remote model use its defaults
        data = {
                "prompt"   : prompt,
                "n_predict": adjusted_max_tokens,
                "stream"   : LLAMACPP_STREAM,
        }

        # Add stop sequences - combine provided stop with default stop sequences
        combined_stop = list(self.default_stop_sequences)  # Start with defaults
        if stop:
            combined_stop.extend(stop)  # Add any provided stop sequences

        # Remove duplicates while preserving order
        seen = set()
        unique_stop = []
        for seq in combined_stop:
            if seq not in seen:
                seen.add(seq)
                unique_stop.append(seq)

        data["stop"] = unique_stop

        # Log request payload (excluding full prompt content if needed, but keep in DEBUG per preferences)
        logger = get_logger()
        logger.debug(
                "LLM request: %s",
                {
                        "url"             : self.api_url,
                        "n_predict"       : adjusted_max_tokens,
                        "stream"          : LLAMACPP_STREAM,
                        "stop"            : unique_stop,
                        "prompt_token_len": self.prompt_manager.prompt_token_length(prompt),
                },
        )
        logger.debug(f"LLM prompt:\n{prompt}")

        try:
            response = requests.post(self.api_url, headers = headers, data = json.dumps(data))

            # Log full raw response for debugging
            logger.debug(f"LLM raw response status: {response.status_code}")
            logger.debug(f"LLM raw response headers: {dict(response.headers)}")
            logger.debug(f"LLM raw response body: {response.text}")

            response.raise_for_status()
            response_json = response.json()

            content = ""
            if "content" in response_json:
                content = response_json["content"]
            elif "response" in response_json:
                content = response_json["response"]
            elif "text" in response_json:
                content = response_json["text"]
            elif "choices" in response_json and len(response_json["choices"]) > 0:
                content = response_json["choices"][0]["text"]
            else:
                content = str(response_json)

            # Check for stop sequence detection before cleaning
            original_content = content
            stop_sequence_detected = None
            for stop_seq in unique_stop:
                if stop_seq in original_content:
                    stop_sequence_detected = stop_seq
                    logger.debug(f"Stop sequence detected: '{stop_seq}' in LLM response")
                    break

            content = self._clean_response_content(content)

            # Log stop sequence detection after cleaning
            if stop_sequence_detected:
                logger.info(f"LLM response properly terminated with stop sequence: '{stop_sequence_detected}'")
            else:
                logger.debug("No stop sequence detected in LLM response - response may be truncated")

            generation = ChatGeneration(
                    message = AIMessage(content = content),
            )

            return ChatResult(generations = [generation])
        except Exception as e:
            raise ValueError(f"Error calling LLaMA.cpp API: {str(e)}")

    def _clean_response_content(self, content: str) -> str:
        """Clean up the response content to remove tool descriptions, repeated prompts, and stop sequences."""
        # Remove stop sequences from content
        for stop_seq in self.default_stop_sequences:
            if stop_seq in content:
                content = content.split(stop_seq)[0].strip()

        if "Available Tools:" in content:
            content = content.split("Available Tools:")[0].strip()

        if "User:" in content:
            content = content.split("User:")[0].strip()

        if content.startswith("Assistant:"):
            content = content[len("Assistant:"):].strip()

        return content

    @log
    def _convert_messages_to_prompt(self, messages: List[BaseMessage]) -> str:
        """Deprecated: formatting now handled by formatter strategy. Kept for compatibility."""
        formatter = self._select_formatter()
        return formatter.format(messages, tools = self.tools)

    def _stream(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager = None,
            **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        """Stream the response from the LLaMA.cpp API."""
        result = self._generate(messages, stop, run_manager, **kwargs)
        if result.generations:
            yield ChatGenerationChunk(message = AIMessageChunk(content = result.generations[0].message.content))

    async def _astream(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager = None,
            **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        """Async stream the response from the LLaMA.cpp API."""
        result = self._generate(messages, stop, run_manager, **kwargs)
        if result.generations:
            yield ChatGenerationChunk(message = AIMessageChunk(content = result.generations[0].message.content))


class LlamaCppProvider:
    """LLaMA.cpp model provider implementation."""

    def __init__(self, config: Dict[str, Any], api_url: Optional[str] = None):
        """Initialize the LLaMA.cpp model provider."""
        self.config = config
        self.api_url = api_url or "http://172.16.0.111:11111/completion"

    @log
    def get_llm(self) -> LlamaCppChatModel:
        """Get the LLaMA.cpp language model instance."""
        return LlamaCppChatModel(
                api_url = self.api_url,
                # Stop sequences implemented: <|im_end|>, <|end_of_text|>, </s>, <|eot_id|>
                # Logging added for stop sequence detection
                max_tokens = LLAMACPP_MAX_TOKENS
        )
