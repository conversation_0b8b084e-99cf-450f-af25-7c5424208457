"""
Base model provider interface for the LangGraph agent.

This module defines the abstract interface that all model providers
must implement to work with the agent framework.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict


class ModelProvider(ABC):
    """Base class for model providers."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the model provider with configuration."""
        self.config = config

    @abstractmethod
    def get_llm(self) -> Any:
        """Get the language model instance."""
        pass
