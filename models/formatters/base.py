"""
Chat formatting strategy interfaces and default generic formatter.
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from langchain.schema.messages import BaseMessage, SystemMessage, HumanMessage, AIMessage, ToolMessage


class ChatFormatter(ABC):
    """
    Strategy interface for converting a sequence of messages to a model-ready prompt string.
    Keep implementations model-agnostic where possible.
    """

    @abstractmethod
    def format(self, messages: List[BaseMessage], tools: Optional[list] = None) -> str:
        """Return prompt string for the model given LangChain messages and optional tools."""
        raise NotImplementedError


class GenericFormatter(ChatFormatter):
    """
    Simple, generic formatter using plain roles like System/User/Assistant.
    This mirrors the previous behavior and acts as a safe fallback when no chat_template is known.
    """

    def format(self, messages: List[BaseMessage], tools: Optional[list] = None) -> str:
        prompt = ""

        if tools:
            prompt += "You have access to the following tools:\n"
            for tool in tools:
                # Best-effort description; avoid depending on specific tool schemas for now
                name = getattr(tool, "name", "tool")
                desc = getattr(tool, "description", "")
                prompt += f"- {name}: {desc}\n"
            prompt += "\nOnly use these tools when necessary. Respond directly when you know the answer.\n\n"

        for message in messages:
            if isinstance(message, SystemMessage):
                prompt += f"System: {message.content}\n\n"
            elif isinstance(message, HumanMessage):
                prompt += f"User: {message.content}\n\n"
            elif isinstance(message, AIMessage):
                prompt += f"Assistant: {message.content}\n\n"
            elif isinstance(message, ToolMessage):
                # Keep tool outputs visible as a separate role
                prompt += f"Tool ({message.tool_call_id}): {message.content}\n\n"
            else:
                # Fallback
                role = getattr(message, "type", "message")
                prompt += f"{role.title()}: {message.content}\n\n"

        prompt += "Assistant: "
        return prompt
