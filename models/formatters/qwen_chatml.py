"""
Qwen-style ChatML formatter.
Formats messages into ChatML tokens expected by Qwen3 Instruct models:
Each message as: <|im_start|>{role}\n{content}<|im_end|> and ends with assistant generation prompt.
Optionally emits tool/function calling instructions block (only description-level for now).
"""
from typing import List, Optional

from langchain.schema.messages import BaseMessage, SystemMessage, HumanMessage, AIMessage, ToolMessage

from .base import ChatFormatter


class QwenChatMLFormatter(ChatFormatter):
    def format(self, messages: List[BaseMessage], tools: Optional[list] = None) -> str:
        parts: List[str] = []

        # Identify leading system message (if present)
        start_idx = 0
        if messages and isinstance(messages[0], SystemMessage):
            parts.append("<|im_start|>system\n" + messages[0].content + "<|im_end|>\n")
            start_idx = 1
        elif tools:
            # If tools exist with no explicit system message, provide minimal system context (per Qwen docs)
            parts.append(
                "<|im_start|>system\nYou are <PERSON>wen, a helpful AI assistant that can interact with tools to solve tasks.<|im_end|>\n")

        # Optional tools description (lightweight, model-agnostic)
        if tools:
            tool_lines = ["You have access to the following functions:", "", "<tools>"]
            for tool in tools:
                name = getattr(tool, "name", "tool")
                desc = getattr(tool, "description", "")
                tool_lines.append(f"<function>\n<name>{name}</name>\n<description>{desc}</description>\n</function>")
            tool_lines.append("</tools>")
            tool_lines.append("")
            tool_lines.append(
                "If you choose to call a function, reply with a <tool_call> block containing a JSON object with 'name' and 'arguments'.")
            tool_block = "\n".join(tool_lines)
            parts.append("<|im_start|>system\n" + tool_block + "<|im_end|>\n")

        # Add remaining messages in ChatML
        for msg in messages[start_idx:]:
            if isinstance(msg, HumanMessage):
                parts.append("<|im_start|>user\n" + msg.content + "<|im_end|>\n")
            elif isinstance(msg, AIMessage):
                parts.append("<|im_start|>assistant\n" + msg.content + "<|im_end|>\n")
            elif isinstance(msg, ToolMessage):
                # Tool responses are treated as special user messages
                parts.append("<|im_start|>user\n<tool_response>\n" + msg.content + "\n</tool_response><|im_end|>\n")
            elif isinstance(msg, SystemMessage):
                parts.append("<|im_start|>system\n" + msg.content + "<|im_end|>\n")
            else:
                # Fallback as user
                parts.append("<|im_start|>user\n" + msg.content + "<|im_end|>\n")

        # Assistant generation prompt
        parts.append("<|im_start|>assistant\n")
        return "".join(parts)
