"""
State definitions for the LangGraph agent.

This module defines a simplified state schema for the basic START -> llm -> END flow.
"""

from typing import List, Optional, Annotated

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict

from logger.get_logger import log


class AgentState(TypedDict):
    """
    Enhanced state schema for the LangGraph agent with context-aware summarization support.

    This state maintains conversation context and summarization based on token limits.
    """
    # Message history with automatic message aggregation
    messages: Annotated[List[BaseMessage], add_messages]

    # Summarization support
    summary: str  # Current conversation summary

    # Current interaction context
    user_input: str  # Latest user input
    final_answer: str  # LLM response

    # Note: Removed _llm from state to avoid serialization issues
    # LLM instances will be created fresh in each node to ensure compatibility

    # Current user input being processed
    user_input: Optional[str]

    # Final answer from LLM
    final_answer: Optional[str]

    # Error handling
    error: Optional[str]


@log
def create_initial_state(user_input: str) -> AgentState:
    """
    Create an initial state for a new agent conversation.

    Args:
        user_input: The user's initial input/question

    Returns:
        Initial AgentState with user input and default values
    """
    return AgentState(
            messages = [HumanMessage(content = user_input)],
            summary = "",  # Empty summary for new conversations
            user_input = user_input,
            final_answer = None,
            error = None
    )
