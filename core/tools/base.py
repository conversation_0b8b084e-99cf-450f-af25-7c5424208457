"""
Base tool classes and registry for LangGraph agent.

This module provides the foundation for converting FastMCP tools to work
with LangGraph while maintaining async compatibility and tool metadata.
"""

import asyncio
import inspect
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Callable, List, Optional

from logger.get_logger import log


@dataclass
class LangGraphTool:
    """
    LangGraph-compatible tool wrapper.
    
    This class wraps both sync and async tools to provide a consistent
    interface for the LangGraph agent while preserving tool metadata.
    """
    name: str
    description: str
    func: Callable
    is_async: bool = False
    parameters: Optional[Dict[str, Any]] = None

    @log
    def execute(self, input_data: str) -> Any:
        """
        Execute the tool with the given input.

        Args:
            input_data: Input string for the tool

        Returns:
            Tool execution result
        """
        try:
            if self.is_async:
                return self._run_async_tool(input_data)
            else:
                return self._run_sync_tool(input_data)
        except Exception as e:
            return f"Error executing {self.name}: {str(e)}"

    def _run_async_tool(self, input_data: str) -> Any:
        """Run an async tool in a sync context."""
        try:
            # Check if we're already in an event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, create a new thread
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.func(input_data))
                    return future.result()
            else:
                # We can run directly
                return asyncio.run(self.func(input_data))
        except RuntimeError:
            # No event loop, run directly
            return asyncio.run(self.func(input_data))

    def _run_sync_tool(self, input_data: str) -> Any:
        """Run a sync tool."""
        return self.func(input_data)


class ToolRegistry:
    """
    Registry for managing LangGraph-compatible tools.
    
    This class maintains a registry of available tools and provides
    methods for tool discovery, registration, and execution.
    """

    def __init__(self):
        """Initialize the tool registry."""
        self.tools: Dict[str, LangGraphTool] = {}
        self._register_default_tools()

    def register_tool(self, tool: LangGraphTool) -> None:
        """
        Register a tool in the registry.

        Args:
            tool: LangGraphTool instance to register
        """
        self.tools[tool.name] = tool

    @log
    def register_function(
            self,
            name: str,
            func: Callable,
            description: str,
            is_async: Optional[bool] = None,
            parameters: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Register a function as a tool.

        Args:
            name: Tool name
            func: Function to register
            description: Tool description
            is_async: Whether the function is async (auto-detected if None)
            parameters: Tool parameters schema
        """
        if is_async is None:
            is_async = inspect.iscoroutinefunction(func)

        tool = LangGraphTool(
                name = name,
                description = description,
                func = func,
                is_async = is_async,
                parameters = parameters
        )

        self.register_tool(tool)

    @log
    def get_tool(self, name: str) -> Optional[LangGraphTool]:
        """
        Get a tool by name.

        Args:
            name: Tool name

        Returns:
            LangGraphTool instance or None if not found
        """
        return self.tools.get(name)

    @log
    def get_available_tools(self) -> List[str]:
        """
        Get list of available tool names.

        Returns:
            List of tool names
        """
        return list(self.tools.keys())

    @log
    def get_tool_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions for all tools.

        Returns:
            Dictionary mapping tool names to descriptions
        """
        return {name: tool.description for name, tool in self.tools.items()}

    @log
    def execute_tool(self, name: str, input_data: str) -> Any:
        """
        Execute a tool by name.

        Args:
            name: Tool name
            input_data: Input for the tool

        Returns:
            Tool execution result
        """
        tool = self.get_tool(name)
        if not tool:
            error_msg = f"Tool '{name}' not found. Available tools: {list(self.tools.keys())}"
            return error_msg

        result = tool.execute(input_data)

        return result

    def _register_default_tools(self) -> None:
        """Register default tools from the local tools modules."""
        try:
            # Import and register datetime tools
            from .local.datetime_tools import (
                get_current_date_tool,
                get_current_time_tool,
                get_current_datetime_tool
            )

            self.register_tool(get_current_date_tool)
            self.register_tool(get_current_time_tool)
            self.register_tool(get_current_datetime_tool)

            # Import and register math tools
            from .local.math_tools import math_calculator_tool
            self.register_tool(math_calculator_tool)

        except ImportError as e:
            pass  # Tools not available


class BaseToolConverter(ABC):
    """
    Abstract base class for converting tools to LangGraph format.
    
    This class provides a framework for converting different types of tools
    (FastMCP, LangChain, custom) to the LangGraph-compatible format.
    """

    @abstractmethod
    def convert_tool(self, tool: Any) -> LangGraphTool:
        """
        Convert a tool to LangGraph format.
        
        Args:
            tool: Tool to convert
            
        Returns:
            LangGraphTool instance
        """
        pass


class FastMCPToolConverter(BaseToolConverter):
    """
    Converter for FastMCP tools to LangGraph format.
    
    This converter handles the conversion of FastMCP tools while preserving
    their async nature and metadata.
    """

    def convert_tool(self, fastmcp_tool: Any) -> LangGraphTool:
        """
        Convert a FastMCP tool to LangGraph format.
        
        Args:
            fastmcp_tool: FastMCP tool instance
            
        Returns:
            LangGraphTool instance
        """
        # Extract tool information from FastMCP tool
        name = getattr(fastmcp_tool, 'name', 'unknown_tool')
        description = self._extract_description(fastmcp_tool)
        func = self._extract_function(fastmcp_tool)
        is_async = inspect.iscoroutinefunction(func)

        return LangGraphTool(
                name = name,
                description = description,
                func = func,
                is_async = is_async
        )

    def _extract_description(self, tool: Any) -> str:
        """Extract description from FastMCP tool."""
        if hasattr(tool, 'description'):
            return tool.description
        elif hasattr(tool, '__doc__') and tool.__doc__:
            return tool.__doc__.strip()
        else:
            return f"Tool: {getattr(tool, 'name', 'unknown')}"

    def _extract_function(self, tool: Any) -> Callable:
        """Extract the callable function from FastMCP tool."""
        if hasattr(tool, 'fn'):
            return tool.fn
        elif hasattr(tool, 'run'):
            return tool.run
        elif callable(tool):
            return tool
        else:
            raise ValueError(f"Could not extract function from tool: {tool}")
