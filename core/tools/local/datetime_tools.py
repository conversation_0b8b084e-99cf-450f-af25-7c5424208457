"""
DateTime tools for the LangGraph agent.

These tools provide date and time functionality, converted from the
original FastMCP implementations to work with LangGraph.
"""

from datetime import datetime

from ..base import LangGraphTool


async def get_current_date(input_text: str = "") -> str:
    """
    Get the current date in YYYY-MM-DD format.
    Use this tool when the user asks for today's date or current date.

    Args:
        input_text: Optional input (not used for this tool)

    Returns:
        Current date in YYYY-MM-DD format
    """
    try:
        return datetime.now().strftime("%Y-%m-%d")
    except Exception as e:
        return f"Error getting current date: {str(e)}"


async def get_current_time(input_text: str = "") -> str:
    """
    Get the current time in HH:MM:SS format.
    Use this tool when the user asks for the current time.

    Args:
        input_text: Optional input (not used for this tool)

    Returns:
        Current time in HH:MM:SS format
    """
    try:
        return datetime.now().strftime("%H:%M:%S")
    except Exception as e:
        return f"Error getting current time: {str(e)}"


async def get_current_datetime(input_text: str = "") -> str:
    """
    Get the current date and time in YYYY-MM-DD HH:MM:SS format.
    Use this tool when the user asks for both date and time.

    Args:
        input_text: Optional input (not used for this tool)

    Returns:
        Current date and time in YYYY-MM-DD HH:MM:SS format
    """
    try:
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        return f"Error getting current datetime: {str(e)}"


# Create LangGraphTool instances
get_current_date_tool = LangGraphTool(
    name="get_current_date",
    description="Get the current date in YYYY-MM-DD format. Use when user asks for today's date.",
    func=get_current_date,
    is_async=True
)

get_current_time_tool = LangGraphTool(
    name="get_current_time", 
    description="Get the current time in HH:MM:SS format. Use when user asks for current time.",
    func=get_current_time,
    is_async=True
)

get_current_datetime_tool = LangGraphTool(
    name="get_current_datetime",
    description="Get current date and time in YYYY-MM-DD HH:MM:SS format. Use when user asks for both date and time.",
    func=get_current_datetime,
    is_async=True
)
