"""
Math tools for the LangGraph agent.

These tools provide mathematical calculation functionality, converted from
the original FastMCP implementations to work with LangGraph.
"""

import math
import re

from ..base import LangGraphTool


async def math_calculator(expression: str) -> str:
    """
    Perform mathematical calculations safely.

    Supports basic arithmetic operations: +, -, *, /, **, (), and common math functions.
    Examples:
    - "2 + 3" -> "5"
    - "15 * 23" -> "345"
    - "sqrt(16)" -> "4.0"
    - "2**3" -> "8"

    Args:
        expression: Mathematical expression to evaluate

    Returns:
        String representation of the calculation result
    """
    try:
        # Clean the expression
        expression = expression.strip()
        
        if not expression:
            return "Error: Empty expression provided"
        
        # Security: Only allow safe characters and functions
        allowed_chars = re.compile(r'^[0-9+\-*/().\s\w]+$')
        if not allowed_chars.match(expression):
            return "Error: Expression contains invalid characters"
        
        # Replace common math function names with math module equivalents
        safe_expression = expression.lower()
        
        # Define safe math functions
        safe_functions = {
            'sqrt': math.sqrt,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'abs': abs,
            'round': round,
            'floor': math.floor,
            'ceil': math.ceil,
            'pi': math.pi,
            'e': math.e
        }
        
        # Replace function names in expression with proper function calls
        for func_name in safe_functions.keys():
            # Replace function calls like sqrt(16) with safe_functions["sqrt"](16)
            pattern = f'{func_name}('
            replacement = f'safe_functions["{func_name}"]('
            safe_expression = safe_expression.replace(pattern, replacement)
        
        # Create safe evaluation environment
        safe_dict = {
            "__builtins__": {},
            "safe_functions": safe_functions,
            "math": math
        }
        
        # Evaluate the expression
        result = eval(safe_expression, safe_dict)
        
        # Format the result
        if isinstance(result, float):
            # Round to reasonable precision
            if result.is_integer():
                return str(int(result))
            else:
                return f"{result:.10g}"  # Remove trailing zeros
        else:
            return str(result)
            
    except ZeroDivisionError:
        return "Error: Division by zero"
    except ValueError as e:
        return f"Error: Invalid mathematical operation - {str(e)}"
    except SyntaxError:
        return "Error: Invalid mathematical expression syntax"
    except Exception as e:
        return f"Error: Could not evaluate expression - {str(e)}"


# Create LangGraphTool instance
math_calculator_tool = LangGraphTool(
    name="math_calculator",
    description="Perform mathematical calculations safely. Supports basic arithmetic (+, -, *, /, **), parentheses, and common math functions (sqrt, sin, cos, tan, log, exp, etc.). Example: '2 + 3 * 4' or 'sqrt(16)'",
    func=math_calculator,
    is_async=True
)
