"""
Market info tools (scaffold).
Provide functions like get_ticker, get_orderbook, get_ohlc.
"""
from typing import Dict, Any


async def get_ticker(symbol: str) -> Dict[str, Any]:
    """Placeholder for fetching ticker data."""
    return {"symbol": symbol, "price": None, "note": "Implement data source"}


async def get_ohlc(symbol: str, interval: str, start: str = None, end: str = None) -> Dict[str, Any]:
    """Placeholder for OHLC retrieval."""
    return {"symbol": symbol, "interval": interval, "ohlc": [], "note": "Implement data source"}
