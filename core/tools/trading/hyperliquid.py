"""
Hyperliquid DEX tools (scaffold).
Provide functions like place_order, cancel_order, get_positions, get_balance.
"""
from typing import Dict, Any


async def place_order(symbol: str, side: str, quantity: float, price: float = None, type: str = "limit") -> Dict[
    str, Any]:
    """Placeholder for placing an order on Hyperliquid."""
    return {
            "symbol"  : symbol,
            "side"    : side,
            "quantity": quantity,
            "price"   : price,
            "type"    : type,
            "status"  : "not_implemented"
    }


async def get_positions() -> Dict[str, Any]:
    return {"positions": [], "note": "Implement API client"}


async def get_balance() -> Dict[str, Any]:
    return {"balance": None, "note": "Implement API client"}
