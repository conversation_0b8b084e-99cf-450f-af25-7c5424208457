from typing import List

from langchain_core.messages import SystemMessage, HumanMessage, BaseMessage

from core.state import AgentState
from logger.get_logger import log, get_logger
from .llm_node import get_cached_llm


class SummaryManager:
    """
    Advanced summary management with intelligent summarization strategies.
    """

    def __init__(self):
        self.logger = get_logger()

    def should_summarize(self, state: AgentState, llm) -> bool:
        """
        Determine if summarization should be triggered based on context limit approach.

        Summarization is triggered when approaching context limit, not after a set amount of messages.

        Args:
            state: Current agent state
            llm: LLM instance with prompt_manager for token counting

        Returns:
            True if summarization should be triggered
        """
        messages = state.get("messages", [])
        summary = state.get("summary", "")

        # Need at least 7 messages to summarize (keep newest 3 human+ai pairs = 6 messages)
        if len(messages) < 7:
            self.logger.debug(f"summarization_node - Not enough messages for summarization: {len(messages)} < 7")
            return False

        # Calculate total tokens that would be sent to LLM
        # System message with summary
        system_content = "You are a helpful Assistant. Provide clear, accurate, and concise responses to user questions."
        if summary.strip():
            system_content += (
                    f"\n\nPrevious conversation context:\n{summary.strip()}\n\n"
                    "Use this context to provide more relevant and personalized responses. "
                    "Reference previous topics when appropriate, but focus on the current question."
            )

        system_tokens = llm.prompt_manager.count_tokens(system_content)

        # All current messages
        message_tokens = sum(llm.prompt_manager.count_tokens(msg.content) for msg in messages)

        total_prompt_tokens = system_tokens + message_tokens

        # Check if approaching context limit (80% threshold)
        max_context = llm.prompt_manager.max_context_tokens
        reserved = llm.prompt_manager.reserved_tokens
        available_tokens = max_context - reserved
        threshold = available_tokens * 0.8

        should_summarize = total_prompt_tokens > threshold

        self.logger.debug(
                f"summarization_node - Token analysis: system={system_tokens}, messages={message_tokens}, "
                f"total={total_prompt_tokens}, threshold={threshold:.0f}, should_summarize={should_summarize}")

        return should_summarize

    def extract_conversation_text(self, messages_to_summarize: List[BaseMessage],
                                  existing_summary: str = "") -> str:
        """
        Extract and format conversation text for summarization.

        Only summarizes the oldest messages, excluding the newest 3 human+ai pairs.

        Args:
            messages_to_summarize: List of oldest messages to be summarized
            existing_summary: Existing conversation summary

        Returns:
            Formatted conversation text
        """
        conversation_parts = []

        # Include existing summary if available
        if existing_summary.strip():
            conversation_parts.append(f"Previous Summary: {existing_summary.strip()}")
            conversation_parts.append("---")

        # Format messages with better structure
        for msg in messages_to_summarize:
            if hasattr(msg, "content") and hasattr(msg, "type"):
                role = "Human" if msg.type == "human" else "Assistant"
                content = msg.content.strip()
                if content:  # Only include non-empty messages
                    conversation_parts.append(f"{role}: {content}")

        return "\n".join(conversation_parts)

    @log
    def create_summary_prompt(self, conversation_text: str, existing_summary: str = "") -> str:
        """
        Create an enhanced summarization prompt focused on reducing token count.

        Args:
            conversation_text: Formatted conversation text
            existing_summary: Existing summary to update

        Returns:
            Enhanced summarization prompt
        """
        if existing_summary.strip():
            return (
                    "You are an expert conversation summarizer. Update the existing summary with new information from the conversation. "
                    "This summary will be sent to an LLM in a SystemMessage to provide context for continuing the conversation. "
                    "Focus on the most important parts of prior messages:\n"
                    "1. Key facts, names, and important details\n"
                    "2. User preferences and context\n"
                    "3. Ongoing topics or tasks\n"
                    "4. Important decisions or conclusions\n\n"
                    "Keep the summary concise but comprehensive. Avoid duplicates and standard AI suggestions. "
                    "The goal is to reduce token count while preserving essential context.\n\n"
                    f"Conversation to summarize:\n{conversation_text}\n\n"
                    "Provide an updated summary that maintains continuity:"
            )
        else:
            return (
                    "You are an expert conversation summarizer. Create a concise summary of this conversation. "
                    "This summary will be sent to an LLM in a SystemMessage to provide context for continuing the conversation. "
                    "Focus on the most important parts of prior messages:\n"
                    "1. Key facts, names, and important details\n"
                    "2. User preferences and context\n"
                    "3. Main topics discussed\n"
                    "4. Important decisions or conclusions\n\n"
                    "Keep the summary brief but capture essential information. Avoid duplicates and standard AI suggestions. "
                    "The goal is to reduce token count while preserving essential context.\n\n"
                    f"Conversation to summarize:\n{conversation_text}\n\n"
                    "Provide a clear, concise summary:"
            )


@log
def summarization_node(state: AgentState) -> AgentState:
    """
    Context-aware summarization node that triggers based on token limits.

    Key improvements:
    - Triggers summarization when approaching context limit, not after set message count
    - Summarizes oldest messages while keeping newest 3 human+ai pairs intact
    - Removes summarized messages from state to prevent sending them to LLM again
    - Focuses on essential context preservation for conversation continuity

    Args:
        state: Current agent state

    Returns:
        Updated state with summary and filtered messages
    """
    logger = get_logger()
    summary_manager = SummaryManager()

    messages = state.get("messages", [])
    existing_summary = state.get("summary", "")

    logger.debug(f"summarization_node - Message count: {len(messages)}, Has summary: {bool(existing_summary)}")

    # Get cached LLM instance for token counting
    llm = get_cached_llm(state)

    # Check if summarization should be triggered based on context limits
    if not summary_manager.should_summarize(state, llm):
        logger.debug("summarization_node - Summarization not triggered: not approaching context limit")
        return state

    logger.debug("summarization_node - Summarization triggered: approaching context limit")

    try:
        # Determine which messages to summarize and which to keep
        # Keep newest 3 human+ai pairs (6 messages), summarize the rest
        messages_to_keep = 6  # 3 human + 3 ai messages

        if len(messages) <= messages_to_keep:
            logger.debug(
                f"summarization_node - Not enough messages to summarize: {len(messages)} <= {messages_to_keep}")
            return state

        messages_to_summarize = messages[:-messages_to_keep]
        messages_to_keep_list = messages[-messages_to_keep:]

        logger.debug(
            f"summarization_node - Summarizing {len(messages_to_summarize)} oldest messages, keeping {len(messages_to_keep_list)} newest")

        # Extract conversation text from messages to be summarized
        conversation_text = summary_manager.extract_conversation_text(
                messages_to_summarize,
                existing_summary
        )

        # Calculate token count for conversation text
        conversation_tokens = llm.prompt_manager.count_tokens(conversation_text)
        logger.debug(
            f"summarization_node - Prepared {conversation_tokens} tokens of conversation text for summarization")

        # Create enhanced summarization prompt
        prompt = summary_manager.create_summary_prompt(conversation_text, existing_summary)

        summarization_msgs = [
                SystemMessage(
                        content = "You are an expert conversation summarizer focused on maintaining context continuity while reducing token count."),
                HumanMessage(content = prompt)
        ]

        # Calculate token counts for summarization
        system_tokens = llm.prompt_manager.count_tokens(summarization_msgs[0].content)
        prompt_tokens = llm.prompt_manager.count_tokens(summarization_msgs[1].content)
        total_summarization_tokens = system_tokens + prompt_tokens

        logger.debug(
            f"summarization_node - Summarization prompt tokens: system={system_tokens}, prompt={prompt_tokens}, total={total_summarization_tokens}")
        logger.debug(f"summarization_node - Sending summarization request to LLM")

        # Generate summary with error handling
        response = llm.invoke(summarization_msgs)
        new_summary = response.content.strip() if response and response.content else ""

        if not new_summary:
            logger.warning("summarization_node - LLM returned empty summary, keeping existing summary")
            new_summary = existing_summary

        # Calculate summary tokens
        summary_tokens = llm.prompt_manager.count_tokens(new_summary) if new_summary else 0
        logger.debug(f"summarization_node - Generated summary ({summary_tokens} tokens): {new_summary}")

        # Update state with new summary and only the newest messages
        # Note: Due to add_messages annotation, we need to clear and rebuild the messages list
        state["summary"] = new_summary

        # Clear the messages list and add only the messages we want to keep
        state["messages"].clear()
        state["messages"].extend(messages_to_keep_list)

        logger.debug(
            f"summarization_node - Updated state: summary with {summary_tokens} tokens, {len(state['messages'])} messages kept")
        logger.debug(f"summarization_node - Summary updated successfully")

    except Exception as e:
        logger.error(f"summarization_node - Error during summarization: {e}")
        # On error, keep the newest 6 messages without updating summary
        if len(messages) > 6:
            newest_messages = messages[-6:]
            state["messages"].clear()
            state["messages"].extend(newest_messages)
            logger.debug(f"summarization_node - Fallback: kept newest 6 messages without summarization due to error")

    return state
