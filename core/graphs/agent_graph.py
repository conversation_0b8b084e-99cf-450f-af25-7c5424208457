"""
Agent graph definition using LangGraph.

This module creates a simple agent graph: START -> llm -> END
"""

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START, END

from ..nodes.llm_node import llm_node
from ..nodes.summarization_node import summarization_node
from ..state import AgentState


def create_agent_graph():
    """
    Create the main agent graph using LangGraph.

    Enhanced graph structure with summarization:
    START -> summarizer -> llm_node -> END

    Returns:
        Compiled LangGraph with memory checkpointing
    """
    # Create the state graph
    graph = StateGraph(AgentState)

    # Add nodes
    graph.add_node("summarizer", summarization_node)
    graph.add_node("llm_node", llm_node)

    # Add edges: START -> summarizer -> llm_node -> END
    graph.add_edge(START, "summarizer")
    graph.add_edge("summarizer", "llm_node")
    graph.add_edge("llm_node", END)

    # Add memory for conversation persistence
    # MemorySaver provides in-memory checkpointing for conversation state
    # This allows the graph to maintain conversation history across interactions
    # within the same thread_id, enabling context-aware responses
    memory = MemorySaver()

    # Compile the graph
    graph = graph.compile(checkpointer = memory)

    return graph
