"""
Graph visualization utilities for the LangGraph agent.

This module provides functions to visualize and log the graph structure
dynamically rather than using hardcoded strings.
"""

from logger.get_logger import log


@log
def log_graph_structure(graph, logger):
    """
    Dynamically generate and log the graph structure.
    
    Args:
        graph: Compiled LangGraph instance
        logger: Logger instance to use for output
    """
    try:
        # Get the graph structure
        graph_structure = graph.get_graph()

        # Extract nodes and edges
        nodes = list(graph_structure.nodes.keys())
        edges = [(edge.source, edge.target) for edge in graph_structure.edges]

        # Log the structure
        logger.debug("📊 Dynamic Graph Structure Analysis:")
        logger.debug(f"🔗 Nodes: {', '.join(nodes)}")

        # Create flow representation
        flow_parts = []
        current = "__start__"
        visited = set()

        while current and current not in visited:
            visited.add(current)
            if current == "__start__":
                flow_parts.append("START")
            elif current == "__end__":
                flow_parts.append("END")
            else:
                flow_parts.append(current)

            # Find next node
            next_node = None
            for source, target in edges:
                if source == current:
                    next_node = target
                    break
            current = next_node

        flow_string = " → ".join(flow_parts)
        logger.info(f"🔧 Graph Flow: {flow_string}")
        logger.info("✅ LangGraph initialized successfully")

    except Exception as e:
        # Fallback to static representation
        logger.info("📊 Graph Structure: START → summarizer → llm_node → END")
        logger.info("🔧 Enhanced LangGraph Flow initialized successfully")
        logger.debug(f"Could not generate dynamic graph structure: {e}")


@log
def get_graph_mermaid(graph):
    """
    Generate Mermaid diagram representation of the graph.
    
    Args:
        graph: Compiled LangGraph instance
        
    Returns:
        Mermaid diagram string or None if generation fails
    """
    try:
        return graph.get_graph().draw_mermaid()
    except Exception as e:
        return None
