"""
Helper utilities for the LangGraph agent.

This module provides utility functions for common operations like
formatting conversation history and extracting information from agent state.
"""

from typing import List, Dict, Any, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage
from logger.get_logger import log


@log
def format_conversation_history(messages: List[BaseMessage]) -> str:
    """
    Format conversation history for display or logging.
    
    Args:
        messages: List of conversation messages
        
    Returns:
        Formatted conversation string
    """
    formatted_lines = []

    for i, message in enumerate(messages):
        if isinstance(message, HumanMessage):
            formatted_lines.append(f"User: {message.content}")
        elif isinstance(message, AIMessage):
            formatted_lines.append(f"Assistant: {message.content}")
        elif isinstance(message, ToolMessage):
            formatted_lines.append(f"Tool Result: {message.content}")
        else:
            formatted_lines.append(f"Message {i}: {message.content}")

    return "\n".join(formatted_lines)


@log
def extract_final_answer(state: Dict[str, Any]) -> Optional[str]:
    """
    Extract the final answer from agent state.

    Args:
        state: Agent state dictionary

    Returns:
        Final answer string or None if not available
    """
    # Check direct final_answer field
    if state.get("final_answer"):
        return state["final_answer"]

    # Check last AI message
    messages = state.get("messages", [])
    for message in reversed(messages):
        if isinstance(message, AIMessage) and message.content:
            # Try to extract final answer from AI message
            content = message.content.lower()
            if "final answer:" in content:
                parts = message.content.split("Final Answer:", 1)
                if len(parts) > 1:
                    return parts[1].strip()

    return None


@log
def format_agent_response(
        final_answer: str,
        reasoning_steps: List[str],
        tool_results: Dict[str, Any],
        show_reasoning: bool = True
) -> str:
    """
    Format a complete agent response for display.
    
    Args:
        final_answer: The final answer to display
        reasoning_steps: List of reasoning steps
        tool_results: Dictionary of tool execution results
        show_reasoning: Whether to include reasoning in the output
        
    Returns:
        Formatted response string
    """
    response_parts = []

    if show_reasoning and reasoning_steps:
        response_parts.append("🤔 Reasoning:")
        for i, step in enumerate(reasoning_steps, 1):
            response_parts.append(f"  {i}. {step}")
        response_parts.append("")

    if tool_results:
        response_parts.append("🔧 Tools Used:")
        for tool_name, result in tool_results.items():
            response_parts.append(f"  • {tool_name}: {result}")
        response_parts.append("")

    response_parts.append(f"✅ Answer: {final_answer}")

    return "\n".join(response_parts)


@log
def sanitize_input(user_input: str) -> str:
    """
    Sanitize user input for safe processing.
    
    Args:
        user_input: Raw user input
        
    Returns:
        Sanitized input string
    """
    # Remove excessive whitespace
    sanitized = " ".join(user_input.split())

    # Remove potentially harmful characters (basic sanitization)
    # This is a simple implementation - enhance based on security requirements
    sanitized = sanitized.replace("\x00", "")  # Remove null bytes

    return sanitized.strip()
